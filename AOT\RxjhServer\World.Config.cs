﻿using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.AOI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RxjhServer;

public partial class World
{
    public static int Pill_Merge_MaxTime { get; internal set; }
    public static Dictionary<int, HatchItemClass> List_HatchItem { get; internal set; }

    public void SetConfig()
	{
		var text = "Get the configuration file path";
		try
		{
			Pill_Merge_MaxTime = int.Parse(Config.IniReadValue("GameServer", "Pill_Merge_MaxTime").Trim());
			ChatAllServer_OnOff = (Config.IniReadValue("GameServer", "ChatAllServer_OnOff").Trim().Length == 0) ? ChatAllServer_OnOff : int.Parse(Config.IniReadValue("GameServer", "ChatAllServer_OnOff").Trim());
			VoHuan_GioiHan_MoiNgay = int.Parse(Config.IniReadValue("GameServer", "VoHuan_GioiHan_MoiNgay").Trim());
			NguongSo_NhipTim = int.Parse(Config.IniReadValue("GameServer", "NguongSo_NhipTim").Trim());
			SuTuHong_Lenh = Config.IniReadValue("GameServer", "SuTuHong_Lenh").Trim();
			TLC_CTC_BatDau_QuangBa = int.Parse(Config.IniReadValue("GameServer", "TLC_CTC_BatDau_QuangBa").Trim());
			Offline_DanhQuai = Config.IniReadValue("GameServer", "Offline_DanhQuai").Trim();
			Offline_BuffMau_DP = Config.IniReadValue("GameServer", "Offline_BuffMau_DP").Trim();
			Offline_Pill_ID = int.Parse(Config.IniReadValue("GameServer", "Offline_Pill_ID").Trim());
			Offline_Nhat_Item = int.Parse(Config.IniReadValue("GameServer", "Offline_Nhat_Item").Trim());
			Offline_TreoMay_PhamVi = int.Parse(Config.IniReadValue("GameServer", "Offline_TreoMay_PhamVi").Trim());
			QuaiVatTuVongBienMatTriHoanThoiGian = int.Parse(Config.IniReadValue("GameServer", "QuaiVatTuVongBienMatTriHoanThoiGian").Trim());
			CauHinhBossTheoDame = Config.IniReadValue("GameServer", "CauHinhBossTheoDame").Trim().Split(';');
			CauHinh_Quai_NhiemVu = Config.IniReadValue("GameServer", "CauHinh_Quai_NhiemVu").Trim().Split(';');
			ServerChoPKHayKhong = int.Parse(Config.IniReadValue("GameServer", "ServerChoPKHayKhong"));
			CoHayKo_Lock_GuiTien_Warehouse = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_Lock_GuiTien_Warehouse"));
			CoHayKo_Lock_RutTien_Warehouse = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_Lock_RutTien_Warehouse"));
			CoHayKo_BOSS_Rot_Item = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_BOSS_Rot_Item"));
			CreateNewJob = (Config.IniReadValue("GameServer", "Create_NEWJob").Trim() == "") ? CreateNewJob : int.Parse(Config.IniReadValue("GameServer", "Create_NEWJob").Trim());
			Phan_Tram_Chia_Drop_Gold = int.Parse(Config.IniReadValue("GameServer", "Phan_Tram_Chia_Drop_Gold"));
			Phan_Tram_Chia_Ky_Nang = int.Parse(Config.IniReadValue("GameServer", "Phan_Tram_Chia_Ky_Nang"));
			Phan_Tram_Chia_Ky_Nang_TT = int.Parse(Config.IniReadValue("GameServer", "Phan_Tram_Chia_Ky_Nang_TT"));
			Phan_Tram_Chia_Exp = int.Parse(Config.IniReadValue("GameServer", "Phan_Tram_Chia_Exp"));
			SilverCoinSquareServerID = int.Parse(Config.IniReadValue("GameServer", "SilverCoinSquareServerID"));
			SilverCoinSquareServerPort = int.Parse(Config.IniReadValue("GameServer", "SilverCoinSquareServerPort"));
			GioiHan_CuongHoa_TrangSuc = int.Parse(Config.IniReadValue("GameServer", "GioiHan_CuongHoa_TrangSuc"));
			TongXacSuat_CuongHoaTrangSuc = Config.IniReadValue("GameServer", "TongXacSuat_CuongHoaTrangSuc").Trim().Split(';');
			TyLe_CuongHoa_TrangSuc1 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc1"));
			TyLe_CuongHoa_TrangSuc2 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc2"));
			TyLe_CuongHoa_TrangSuc3 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc3"));
			TyLe_CuongHoa_TrangSuc4 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc4"));
			TyLe_CuongHoa_TrangSuc5 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc5"));
			TyLe_CuongHoa_TrangSuc6 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc6"));
			TyLe_CuongHoa_TrangSuc7 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc7"));
			TyLe_CuongHoa_TrangSuc8 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc8"));
			TyLe_CuongHoa_TrangSuc9 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc9"));
			TyLe_CuongHoa_TrangSuc10 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc10"));
			TyLe_CuongHoa_TrangSuc11 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc11"));
			TyLe_CuongHoa_TrangSuc12 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc12"));
			TyLe_CuongHoa_TrangSuc13 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc13"));
			TyLe_CuongHoa_TrangSuc14 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc14"));
			TyLe_CuongHoa_TrangSuc15 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc15"));
			TyLe_CuongHoa_TrangSuc16 = int.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_TrangSuc16"));
			ItemHoatDongWorldBoss = Config.IniReadValue("GameServer", "ItemHoatDongWorldBoss").Trim().Length == 0
				? ItemHoatDongWorldBoss
				: int.Parse(Config.IniReadValue("GameServer", "ItemHoatDongWorldBoss").Trim());
			CuongHoa_ThanThu_1_10 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_1_10"));
			CuongHoa_ThanThu_11_15 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_11_15"));
			CuongHoa_ThanThu_16_20 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_16_20"));
			CuongHoa_ThanThu_21_25 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_21_25"));
			CuongHoa_ThanThu_26_30 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_26_30"));
			CuongHoa_ThanThu_31_35 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_31_35"));
			CuongHoa_ThanThu_36_40 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_36_40"));
			CuongHoa_ThanThu_41_50 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_41_50"));
			CuongHoa_ThanThu_51_60 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_51_60"));
			CuongHoa_ThanThu_61_70 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_61_70"));
			CuongHoa_ThanThu_71_80 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_71_80"));
			CuongHoa_ThanThu_81_85 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_81_85"));
			CuongHoa_ThanThu_86_90 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_86_90"));
			CuongHoa_ThanThu_91_95 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_91_95"));
			CuongHoa_ThanThu_96_99 = int.Parse(Config.IniReadValue("GameServer", "CuongHoa_ThanThu_96_99"));
			HopThanh_Pet_Dong_1 = int.Parse(Config.IniReadValue("GameServer", "HopThanh_Pet_Dong_1"));
			HopThanh_Pet_Dong_2 = int.Parse(Config.IniReadValue("GameServer", "HopThanh_Pet_Dong_2"));
			HopThanh_Pet_Dong_3 = int.Parse(Config.IniReadValue("GameServer", "HopThanh_Pet_Dong_3"));
			HopThanh_Pet_Dong_4 = int.Parse(Config.IniReadValue("GameServer", "HopThanh_Pet_Dong_4"));
			Rate_Pet_CuongHoa_Level_1 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_1"));
			Rate_Pet_CuongHoa_Level_2 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_2"));
			Rate_Pet_CuongHoa_Level_3 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_3"));
			Rate_Pet_CuongHoa_Level_4 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_4"));
			Rate_Pet_CuongHoa_Level_5 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_5"));
			Rate_Pet_CuongHoa_Level_6 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_6"));
			Rate_Pet_CuongHoa_Level_7 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_7"));
			Rate_Pet_CuongHoa_Level_8 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_8"));
			Rate_Pet_CuongHoa_Level_9 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_9"));
			Rate_Pet_CuongHoa_Level_10 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_10"));
			Rate_Pet_CuongHoa_Level_11 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_11"));
			Rate_Pet_CuongHoa_Level_12 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_12"));
			Rate_Pet_CuongHoa_Level_13 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_13"));
			Rate_Pet_CuongHoa_Level_14 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_14"));
			Rate_Pet_CuongHoa_Level_15 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_15"));
			Rate_Pet_CuongHoa_Level_16 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_16"));
			Rate_Pet_CuongHoa_Level_17 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_17"));
			Rate_Pet_CuongHoa_Level_18 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_18"));
			Rate_Pet_CuongHoa_Level_19 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_19"));
			Rate_Pet_CuongHoa_Level_20 = int.Parse(Config.IniReadValue("GameServer", "Rate_Pet_CuongHoa_Level_20"));
			Mo_TT_1 = int.Parse(Config.IniReadValue("GameServer", "Mo_TT_1"));
			Mo_TT_2 = int.Parse(Config.IniReadValue("GameServer", "Mo_TT_2"));
			Mo_TT_3 = int.Parse(Config.IniReadValue("GameServer", "Mo_TT_3"));
			Mo_TT_4 = int.Parse(Config.IniReadValue("GameServer", "Mo_TT_4"));
			Mo_TT_5 = int.Parse(Config.IniReadValue("GameServer", "Mo_TT_5"));
			Mo_TT_6 = int.Parse(Config.IniReadValue("GameServer", "Mo_TT_6"));
			Mo_TT_7 = int.Parse(Config.IniReadValue("GameServer", "Mo_TT_7"));
			CoHayKo_MoRa_Log_TreoShop = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_MoRa_Log_TreoShop"));
			CoHayKhongMoRa_CongDichChuyen = int.Parse(Config.IniReadValue("GameServer", "CoHayKhongMoRa_CongDichChuyen"));
			GhiLogMuaNPC = int.Parse(Config.IniReadValue("GameServer", "GhiLogMuaNPC"));
			GhiLogBanNPC = int.Parse(Config.IniReadValue("GameServer", "GhiLogBanNPC"));
			GhiLogMoHop = int.Parse(Config.IniReadValue("GameServer", "GhiLogMoHop"));
			GhiLogTienHoaThanThu = int.Parse(Config.IniReadValue("GameServer", "GhiLogTienHoaThanThu"));
			GhiLogCuongHoaThanThu = int.Parse(Config.IniReadValue("GameServer", "GhiLogCuongHoaThanThu"));
			GhiLogCuongHoaThanThu_GiaTri_Array = Config.IniReadValue("GameServer", "GhiLogCuongHoaThanThu_GiaTri").Trim().Split(';');
			GhiLogCuongHoaAoChoang = int.Parse(Config.IniReadValue("GameServer", "GhiLogCuongHoaAoChoang"));
			GhiLogCuongHoaAoChoang_GiaTri_Array = Config.IniReadValue("GameServer", "GhiLogCuongHoaAoChoang_GiaTri").Trim().Split(';');
			GhiLogBuaCuongHoa = int.Parse(Config.IniReadValue("GameServer", "GhiLogBuaCuongHoa"));
			GhiLogBuaCuongHoa_GiaTri_Array = Config.IniReadValue("GameServer", "GhiLogBuaCuongHoa_GiaTri").Trim().Split(';');
			GhiLogTyLeHopThanh = int.Parse(Config.IniReadValue("GameServer", "GhiLogTyLeHopThanh"));
			GhiLogTyLeHopThanh_HoaLongThach = int.Parse(Config.IniReadValue("GameServer", "GhiLogTyLeHopThanh_HoaLongThach"));
			GhiLogCuongHoa = int.Parse(Config.IniReadValue("GameServer", "GhiLogCuongHoa"));
			GhiLogCuongHoa_GiaTri_Array = Config.IniReadValue("GameServer", "GhiLogCuongHoa_GiaTri").Trim().Split(';');
			GhiLogCuongHoaTrangSuc = int.Parse(Config.IniReadValue("GameServer", "GhiLogCuongHoaTrangSuc"));
			GhiLogCuongHoaTrangSuc_GiaTri_Array = Config.IniReadValue("GameServer", "GhiLogCuongHoaTrangSuc_GiaTri").Trim().Split(';');
			GhiLogCuongHoaThucTinh = int.Parse(Config.IniReadValue("GameServer", "GhiLogCuongHoaThucTinh"));
			GhiLogCuongHoaThucTinh_GiaTri_Array = Config.IniReadValue("GameServer", "GhiLogCuongHoaThucTinh_GiaTri").Trim().Split(';');
			PhanThuong_ThemVao_TLC = int.Parse(Config.IniReadValue("GameServer", "PhanThuong_ThemVao_TLC"));
			CoHayKhongMoRaGuiThu = int.Parse(Config.IniReadValue("GameServer", "CoHayKhongMoRaGuiThu"));
			Co_Hay_Khong_Thuong_Them_X2VoHuan_X300_TLC = int.Parse(Config.IniReadValue("GameServer", "Co_Hay_Khong_Thuong_Them_X2VoHuan_X300_TLC"));
			Gioi_han_EXP_khi_co_TLC = int.Parse(Config.IniReadValue("GameServer", "Gioi_han_EXP_khi_co_TLC"));
			CoHayKo_BatTat_Event_Exp_TanThu = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_BatTat_Event_Exp_TanThu").Trim());
			CoHayKo_ON_OFF_Event_Exp_CuoiTuan = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_ON_OFF_Event_Exp_CuoiTuan").Trim());
			CoHayKo_Auto_Kick_Feed_TLC = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_Auto_Kick_Feed_TLC").Trim());
			EventExpALL_Recovery = int.Parse(Config.IniReadValue("GameServer", "EventExpALL_Recovery").Trim());
			CheckBugGold_Recovery = int.Parse(Config.IniReadValue("GameServer", "CheckBugGold_Recovery").Trim());
			CoHayKo_KTTX_KoCan_VatPham_HoTro = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_KTTX_KoCan_VatPham_HoTro").Trim());
			CoHayKhong_MoRa_Backup_Database = int.Parse(Config.IniReadValue("GameServer", "CoHayKhong_MoRa_Backup_Database").Trim());
			Backup_MoRa_Gio = int.Parse(Config.IniReadValue("GameServer", "Backup_MoRa_Gio").Trim());
			Backup_MoRa_Phut = int.Parse(Config.IniReadValue("GameServer", "Backup_MoRa_Phut").Trim());
			Backup_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "Backup_MoRa_Giay").Trim());
			
			ON_OFF_Event_Boss_Map = int.Parse(Config.IniReadValue("GameServer", "ON_OFF_Event_Boss_Map").Trim());
			Boss_Map_Gio = int.Parse(Config.IniReadValue("GameServer", "Boss_Map_Gio").Trim());
			Boss_Map_Phut = int.Parse(Config.IniReadValue("GameServer", "Boss_Map_Phut").Trim());
			Boss_Map_Giay = int.Parse(Config.IniReadValue("GameServer", "Boss_Map_Giay").Trim());
			Boss_Map_Gio_Time_2 = int.Parse(Config.IniReadValue("GameServer", "Boss_Map_Gio_Time_2").Trim());
			Boss_Map_Phut_Time_2 = int.Parse(Config.IniReadValue("GameServer", "Boss_Map_Phut_Time_2").Trim());
			Boss_Map_Giay_Time_2 = int.Parse(Config.IniReadValue("GameServer", "Boss_Map_Giay_Time_2").Trim());
			ON_OFF_Kick_Mem_Ban_888 = int.Parse(Config.IniReadValue("GameServer", "ON_OFF_Kick_Mem_Ban_888").Trim());
			Debug = int.Parse(Config.IniReadValue("GameServer", "Debug").Trim());
			BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu = int.Parse(Config.IniReadValue("GameServer", "BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu"));
			Rate_Gold_Party = (Config.IniReadValue("GameServer", "Rate_Gold_Party") == "") ? Rate_Gold_Party : double.Parse(Config.IniReadValue("GameServer", "Rate_Gold_Party"));
			Rate_Exp_Party = (Config.IniReadValue("GameServer", "Rate_Exp_Party") == "") ? Rate_Exp_Party : double.Parse(Config.IniReadValue("GameServer", "Rate_Exp_Party"));
			Rate_KyNang_TT_Party = (Config.IniReadValue("GameServer", "Rate_KyNang_TT_Party") == "") ? Rate_KyNang_TT_Party : double.Parse(Config.IniReadValue("GameServer", "Rate_KyNang_TT_Party"));
			Rate_KyNang_Party = (Config.IniReadValue("GameServer", "Rate_KyNang_Party") == "") ? Rate_KyNang_Party : double.Parse(Config.IniReadValue("GameServer", "Rate_KyNang_Party"));
			Drop_Cach_1_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_1_Level_Quai") == "") ? Drop_Cach_1_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_1_Level_Quai"));
			Drop_Cach_2_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_2_Level_Quai") == "") ? Drop_Cach_2_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_2_Level_Quai"));
			Drop_Cach_3_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_3_Level_Quai") == "") ? Drop_Cach_3_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_3_Level_Quai"));
			Drop_Cach_4_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_4_Level_Quai") == "") ? Drop_Cach_4_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_4_Level_Quai"));
			Drop_Cach_5_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_5_Level_Quai") == "") ? Drop_Cach_5_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_5_Level_Quai"));
			Drop_Cach_6_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_6_Level_Quai") == "") ? Drop_Cach_6_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_6_Level_Quai"));
			Drop_Cach_7_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_7_Level_Quai") == "") ? Drop_Cach_7_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_7_Level_Quai"));
			Drop_Cach_8_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_8_Level_Quai") == "") ? Drop_Cach_8_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_8_Level_Quai"));
			Drop_Cach_9_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_9_Level_Quai") == "") ? Drop_Cach_9_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_9_Level_Quai"));
			Drop_Cach_10_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_10_Level_Quai") == "") ? Drop_Cach_10_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_10_Level_Quai"));
			Drop_Cach_11_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_11_Level_Quai") == "") ? Drop_Cach_11_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_11_Level_Quai"));
			Drop_Cach_12_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_12_Level_Quai") == "") ? Drop_Cach_12_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_12_Level_Quai"));
			Drop_Cach_13_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_13_Level_Quai") == "") ? Drop_Cach_13_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_13_Level_Quai"));
			Drop_Cach_14_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_14_Level_Quai") == "") ? Drop_Cach_14_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_14_Level_Quai"));
			Drop_Cach_15_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_15_Level_Quai") == "") ? Drop_Cach_15_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_15_Level_Quai"));
			Drop_Cach_16_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_16_Level_Quai") == "") ? Drop_Cach_16_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_16_Level_Quai"));
			Drop_Cach_17_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_17_Level_Quai") == "") ? Drop_Cach_17_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_17_Level_Quai"));
			Drop_Cach_18_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_18_Level_Quai") == "") ? Drop_Cach_18_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_18_Level_Quai"));
			Drop_Cach_19_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_19_Level_Quai") == "") ? Drop_Cach_19_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_19_Level_Quai"));
			Drop_Cach_20_Level_Quai = (Config.IniReadValue("GameServer", "Drop_Cach_20_Level_Quai") == "") ? Drop_Cach_20_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Drop_Cach_20_Level_Quai"));
			Giam_Gold_Drop_Level_100_114 = (Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_100_114") == "") ? Giam_Gold_Drop_Level_100_114 : double.Parse(Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_100_114"));
			Giam_Gold_Drop_Level_115_119 = (Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_115_119") == "") ? Giam_Gold_Drop_Level_115_119 : double.Parse(Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_115_119"));
			Giam_Gold_Drop_Level_120_124 = (Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_120_124") == "") ? Giam_Gold_Drop_Level_120_124 : double.Parse(Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_120_124"));
			Giam_Gold_Drop_Level_125_128 = (Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_125_128") == "") ? Giam_Gold_Drop_Level_125_128 : double.Parse(Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_125_128"));
			Giam_Gold_Drop_Level_129_130 = (Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_129_130") == "") ? Giam_Gold_Drop_Level_129_130 : double.Parse(Config.IniReadValue("GameServer", "Giam_Gold_Drop_Level_129_130"));
			Gold_Drop_Cach_1_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_1_Level_Quai") == "") ? Gold_Drop_Cach_1_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_1_Level_Quai"));
			Gold_Drop_Cach_2_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_2_Level_Quai") == "") ? Gold_Drop_Cach_2_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_2_Level_Quai"));
			Gold_Drop_Cach_3_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_3_Level_Quai") == "") ? Gold_Drop_Cach_3_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_3_Level_Quai"));
			Gold_Drop_Cach_4_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_4_Level_Quai") == "") ? Gold_Drop_Cach_4_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_4_Level_Quai"));
			Gold_Drop_Cach_5_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_5_Level_Quai") == "") ? Gold_Drop_Cach_5_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_5_Level_Quai"));
			Gold_Drop_Cach_6_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_6_Level_Quai") == "") ? Gold_Drop_Cach_6_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_6_Level_Quai"));
			Gold_Drop_Cach_7_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_7_Level_Quai") == "") ? Gold_Drop_Cach_7_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_7_Level_Quai"));
			Gold_Drop_Cach_8_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_8_Level_Quai") == "") ? Gold_Drop_Cach_8_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_8_Level_Quai"));
			Gold_Drop_Cach_9_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_9_Level_Quai") == "") ? Gold_Drop_Cach_9_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_9_Level_Quai"));
			Gold_Drop_Cach_10_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_10_Level_Quai") == "") ? Gold_Drop_Cach_10_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_10_Level_Quai"));
			Gold_Drop_Cach_11_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_11_Level_Quai") == "") ? Gold_Drop_Cach_11_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_11_Level_Quai"));
			Gold_Drop_Cach_12_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_12_Level_Quai") == "") ? Gold_Drop_Cach_12_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_12_Level_Quai"));
			Gold_Drop_Cach_13_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_13_Level_Quai") == "") ? Gold_Drop_Cach_13_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_13_Level_Quai"));
			Gold_Drop_Cach_14_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_14_Level_Quai") == "") ? Gold_Drop_Cach_14_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_14_Level_Quai"));
			Gold_Drop_Cach_15_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_15_Level_Quai") == "") ? Gold_Drop_Cach_15_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_15_Level_Quai"));
			Gold_Drop_Cach_16_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_16_Level_Quai") == "") ? Gold_Drop_Cach_16_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_16_Level_Quai"));
			Gold_Drop_Cach_17_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_17_Level_Quai") == "") ? Gold_Drop_Cach_17_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_17_Level_Quai"));
			Gold_Drop_Cach_18_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_18_Level_Quai") == "") ? Gold_Drop_Cach_18_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_18_Level_Quai"));
			Gold_Drop_Cach_19_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_19_Level_Quai") == "") ? Gold_Drop_Cach_19_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_19_Level_Quai"));
			Gold_Drop_Cach_20_Level_Quai = (Config.IniReadValue("GameServer", "Gold_Drop_Cach_20_Level_Quai") == "") ? Gold_Drop_Cach_20_Level_Quai : double.Parse(Config.IniReadValue("GameServer", "Gold_Drop_Cach_20_Level_Quai"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19"));
			Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20 = (Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20") == "") ? Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20 : double.Parse(Config.IniReadValue("GameServer", "Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20"));
			Bonus_Drop_BanDo_TDKH = (Config.IniReadValue("GameServer", "Bonus_Drop_BanDo_TDKH") == "") ? Bonus_Drop_BanDo_TDKH : double.Parse(Config.IniReadValue("GameServer", "Bonus_Drop_BanDo_TDKH"));
			Time_Delay_Auto_Offline = (Config.IniReadValue("GameServer", "Time_Delay_Auto_Offline") == "") ? Time_Delay_Auto_Offline : double.Parse(Config.IniReadValue("GameServer", "Time_Delay_Auto_Offline"));
			MaxKhiCong_Tren1KhiCong = int.Parse(Config.IniReadValue("GameServer", "MaxKhiCong_Tren1KhiCong").Trim());
			CoHayKo_Bat_Tat_TangHinh_Ninja = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_Bat_Tat_TangHinh_Ninja"));
			Khoa_ChucNang_Auto = int.Parse(Config.IniReadValue("GameServer", "Khoa_ChucNang_Auto"));
			MainServer = (Config.IniReadValue("GameServer", "MainServer").Trim().Length == 0) ? MainServer : int.Parse(Config.IniReadValue("GameServer", "MainServer").Trim());
			Diem_TLC_Chinh = (Config.IniReadValue("GameServer", "Diem_TLC_Chinh").Length == 0) ? Diem_TLC_Chinh : double.Parse(Config.IniReadValue("GameServer", "Diem_TLC_Chinh"));
			Diem_TLC_Ta = (Config.IniReadValue("GameServer", "Diem_TLC_Ta").Length == 0) ? Diem_TLC_Ta : double.Parse(Config.IniReadValue("GameServer", "Diem_TLC_Ta"));
			ResetNhoHon_KhongTheVaoTLC_OLD = (Config.IniReadValue("GameServer", "ResetNhoHon_KhongTheVaoTLC_OLD").Length == 0) ? ResetNhoHon_KhongTheVaoTLC_OLD : int.Parse(Config.IniReadValue("GameServer", "ResetNhoHon_KhongTheVaoTLC_OLD"));
			ResetLonHon_KhongTheVaoTLC_NEW = (Config.IniReadValue("GameServer", "ResetLonHon_KhongTheVaoTLC_NEW").Length == 0) ? ResetLonHon_KhongTheVaoTLC_NEW : int.Parse(Config.IniReadValue("GameServer", "ResetLonHon_KhongTheVaoTLC_NEW"));
			GioiHan_LopNhanVat_KhongDuocKhoiTao = (Config.IniReadValue("GameServer", "GioiHan_LopNhanVat_KhongDuocKhoiTao").Trim().Length == 0) ? GioiHan_LopNhanVat_KhongDuocKhoiTao : Config.IniReadValue("GameServer", "GioiHan_LopNhanVat_KhongDuocKhoiTao").Trim();
			Gioi_han_chenh_lech_2_ben_vao_TLC = (Config.IniReadValue("GameServer", "Gioi_han_chenh_lech_2_ben_vao_TLC").Length == 0) ? Gioi_han_chenh_lech_2_ben_vao_TLC : int.Parse(Config.IniReadValue("GameServer", "Gioi_han_chenh_lech_2_ben_vao_TLC"));
			Gioi_han_acc_vao_TLC = (Config.IniReadValue("GameServer", "Gioi_han_acc_vao_TLC").Length == 0) ? Gioi_han_acc_vao_TLC : int.Parse(Config.IniReadValue("GameServer", "Gioi_han_acc_vao_TLC"));
			Hieu_Ung_Ao_Choang_Chinh_Nam = (Config.IniReadValue("GameServer", "Hieu_Ung_Ao_Choang_Chinh_Nam").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "Hieu_Ung_Ao_Choang_Chinh_Nam").Trim()) : 0;
			Hieu_Ung_Ao_Choang_Chinh_Nu = (Config.IniReadValue("GameServer", "Hieu_Ung_Ao_Choang_Chinh_Nu").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "Hieu_Ung_Ao_Choang_Chinh_Nu").Trim()) : 0;
			Hieu_Ung_Ao_Choang_Ta_Nam = (Config.IniReadValue("GameServer", "Hieu_Ung_Ao_Choang_Ta_Nam").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "Hieu_Ung_Ao_Choang_Ta_Nam").Trim()) : 0;
			Hieu_Ung_Ao_Choang_Ta_Nu = (Config.IniReadValue("GameServer", "Hieu_Ung_Ao_Choang_Ta_Nu").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "Hieu_Ung_Ao_Choang_Ta_Nu").Trim()) : 0;
			PhanThuongTop20_TLC = (Config.IniReadValue("GameServer", "PhanThuongTop20_TLC").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "PhanThuongTop20_TLC").Trim()) : 0;
			PhanThuongBenThangTLC = (Config.IniReadValue("GameServer", "PhanThuongBenThangTLC").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "PhanThuongBenThangTLC").Trim()) : 0;
			PhanThuongBenThuaTLC = (Config.IniReadValue("GameServer", "PhanThuongBenThuaTLC").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "PhanThuongBenThuaTLC").Trim()) : 0;
			MaximumConnectionTimeOfTheGameLoginPort = (Config.IniReadValue("GameServer", "MaximumConnectionTimeOfTheGameLoginPort").Length == 0) ? MaximumConnectionTimeOfTheGameLoginPort : int.Parse(Config.IniReadValue("GameServer", "MaximumConnectionTimeOfTheGameLoginPort"));
			AutomaticConnectionTime = (Config.IniReadValue("GameServer", "AutomaticConnectionTime").Length == 0) ? AutomaticConnectionTime : int.Parse(Config.IniReadValue("GameServer", "AutomaticConnectionTime"));
			Disconnect = Config.IniReadValue("GameServer", "Disconnect").Trim().ToLower() == "true";
			Key = Config.IniReadValue("GameServer", "Key").Trim();
			KinhNghiem_BoiSo = (Config.IniReadValue("GameServer", "KinhNghiem_BoiSo").Trim().Length == 0) ? KinhNghiem_BoiSo : int.Parse(Config.IniReadValue("GameServer", "KinhNghiem_BoiSo").Trim());
			Tien_BoiSo = (Config.IniReadValue("GameServer", "Tien_BoiSo").Trim().Length == 0) ? Tien_BoiSo : int.Parse(Config.IniReadValue("GameServer", "Tien_BoiSo").Trim());
			TyLe_HapHon = (Config.IniReadValue("GameServer", "TyLe_HapHon").Trim().Length == 0) ? TyLe_HapHon : int.Parse(Config.IniReadValue("GameServer", "TyLe_HapHon").Trim());
			LichLuyenBoiSo = (Config.IniReadValue("GameServer", "LichLuyenBoiSo").Trim().Length == 0) ? LichLuyenBoiSo : int.Parse(Config.IniReadValue("GameServer", "LichLuyenBoiSo").Trim());
			ThangThien_LichLuyen_BoiSo = (Config.IniReadValue("GameServer", "ThangThien_LichLuyen_BoiSo").Trim().Length == 0) ? ThangThien_LichLuyen_BoiSo : double.Parse(Config.IniReadValue("GameServer", "ThangThien_LichLuyen_BoiSo").Trim());
			Rate_Drop_Server = (Config.IniReadValue("GameServer", "Rate_Drop_Server").Trim().Length == 0) ? Rate_Drop_Server : int.Parse(Config.IniReadValue("GameServer", "Rate_Drop_Server").Trim());
			MaximumOnline = (Config.IniReadValue("GameServer", "MaximumOnline").Trim().Length == 0) ? MaximumOnline : int.Parse(Config.IniReadValue("GameServer", "MaximumOnline").Trim());
			ServerGroupID = (Config.IniReadValue("GameServer", "ServerGroupID").Trim().Length == 0) ? ServerGroupID : int.Parse(Config.IniReadValue("GameServer", "ServerGroupID").Trim());
			// ServerID = (Config.IniReadValue("GameServer", "ServerID").Trim().Length == 0) ? ServerID : int.Parse(Config.IniReadValue("GameServer", "ServerID").Trim());
			GameServerPort = (Config.IniReadValue("GameServer", "GameServerPort").Trim().Length == 0) ? GameServerPort : int.Parse(Config.IniReadValue("GameServer", "GameServerPort").Trim());
			BaibaogeServerPort = (Config.IniReadValue("GameServer", "BaibaogeServerPort").Trim().Length == 0) ? BaibaogeServerPort : int.Parse(Config.IniReadValue("GameServer", "BaibaogeServerPort").Trim());
			AccountVerificationServerPort = (Config.IniReadValue("GameServer", "AccountVerificationServerPort").Trim().Length == 0) ? AccountVerificationServerPort : int.Parse(Config.IniReadValue("GameServer", "AccountVerificationServerPort").Trim());
			AccountVerificationServerIP = Config.IniReadValue("GameServer", "AccountVerificationServerIP").Trim();
			
			ThongBao_KhiVaoGame = Config.IniReadValue("GameServer", "ThongBao_KhiVaoGame").Trim();
			BaibaoCourtAddress = Config.IniReadValue("GameServer", "BaibaoCourtAddress").Trim();
			ServerName = Config.IniReadValue("GameServer", "ServerName").Trim();
			VIPLine = (Config.IniReadValue("GameServer", "VIPLine").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "VIPLine").Trim()) : 0;
			CoHayKo_check_su_dung_SK1 = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_check_su_dung_SK1").Trim());
			Restart_Recovery = int.Parse(Config.IniReadValue("GameServer", "Restart_Recovery").Trim());
			Check_LoginAcc_Recovery = int.Parse(Config.IniReadValue("GameServer", "Check_LoginAcc_Recovery").Trim());
			Check_LoginAcc_2_Recovery = int.Parse(Config.IniReadValue("GameServer", "Check_LoginAcc_2_Recovery").Trim());
			Time_Auto_Mo_Tuyet_Roi = int.Parse(Config.IniReadValue("GameServer", "Time_Auto_Mo_Tuyet_Roi").Trim());
			CapDoChoPhepGiaoDich = int.Parse(Config.IniReadValue("GameServer", "CapDoChoPhepGiaoDich").Trim());
			DelayBomMau_KhiPK = int.Parse(Config.IniReadValue("GameServer", "DelayBomMau_KhiPK").Trim());
			CoHayKo_Drop_CoMoThongBao = int.Parse(Config.IniReadValue("GameServer", "CoHayKo_Drop_CoMoThongBao").Trim());
			CoHayKhong_Trung_IP_PK_Tinh_Diem_TLC = int.Parse(Config.IniReadValue("GameServer", "CoHayKhong_Trung_IP_PK_Tinh_Diem_TLC").Trim());
			TheLucChien_Random_MoRa = int.Parse(Config.IniReadValue("GameServer", "TheLucChien_Random_MoRa").Trim());
			TheLucChien_MoRa = int.Parse(Config.IniReadValue("GameServer", "TheLucChien_MoRa").Trim());
			TheLucChien_MoRa_Gio = int.Parse(Config.IniReadValue("GameServer", "TheLucChien_MoRa_Gio").Trim());
			TheLucChien_MoRa_Phut = int.Parse(Config.IniReadValue("GameServer", "TheLucChien_MoRa_Phut").Trim());
			TheLucChien_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "TheLucChien_MoRa_Giay").Trim());
			TheLucChien_TongThoiGian = int.Parse(Config.IniReadValue("GameServer", "TheLucChien_TongThoiGian").Trim());
			ThoiGianChuanBi_ChoTheLucChien = int.Parse(Config.IniReadValue("GameServer", "ThoiGianChuanBi_ChoTheLucChien").Trim());
			Thoi_Gian_Ket_Thuc_Giam_Kinh_Nghiem_nho_hon_hoac_bang = int.Parse(Config.IniReadValue("GameServer", "Thoi_Gian_Ket_Thuc_Giam_Kinh_Nghiem_nho_hon_hoac_bang").Trim());
			Time_KichHoat_HopTraiNghiem_Gio = int.Parse(Config.IniReadValue("GameServer", "Time_KichHoat_HopTraiNghiem_Gio").Trim());
			Time_KichHoat_HopTraiNghiem_Phut = int.Parse(Config.IniReadValue("GameServer", "Time_KichHoat_HopTraiNghiem_Phut").Trim());
			Time_KichHoat_HopTraiNghiem_Giay = int.Parse(Config.IniReadValue("GameServer", "Time_KichHoat_HopTraiNghiem_Giay").Trim());
			Time_Huy_HopTraiNghiem_Gio = int.Parse(Config.IniReadValue("GameServer", "Time_Huy_HopTraiNghiem_Gio").Trim());
			Time_Huy_HopTraiNghiem_Phut = int.Parse(Config.IniReadValue("GameServer", "Time_Huy_HopTraiNghiem_Phut").Trim());
			Time_Huy_HopTraiNghiem_Giay = int.Parse(Config.IniReadValue("GameServer", "Time_Huy_HopTraiNghiem_Giay").Trim());
			Bat_Tat_SuKien_TetDoanNgo = int.Parse(Config.IniReadValue("GameServer", "Bat_Tat_SuKien_TetDoanNgo").Trim());
			TetDoanNgo_Gio = int.Parse(Config.IniReadValue("GameServer", "TetDoanNgo_Gio").Trim());
			TetDoanNgo_Phut = int.Parse(Config.IniReadValue("GameServer", "TetDoanNgo_Phut").Trim());
			TetGiapThin_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "TetGiapThin_MoRa_Giay").Trim());
			ON_OFF_Event_HaiThuoc_NamLam = int.Parse(Config.IniReadValue("GameServer", "ON_OFF_Event_HaiThuoc_NamLam").Trim());
			Gold_Nhan_Hop_Qua_HKGH = int.Parse(Config.IniReadValue("GameServer", "Gold_Nhan_Hop_Qua_HKGH").Trim());
			HaiThuoc_NamLam_Gio = int.Parse(Config.IniReadValue("GameServer", "HaiThuoc_NamLam_Gio").Trim());
			HaiThuoc_NamLam_Phut = int.Parse(Config.IniReadValue("GameServer", "HaiThuoc_NamLam_Phut").Trim());
			HaiThuoc_NamLam_Giay = int.Parse(Config.IniReadValue("GameServer", "HaiThuoc_NamLam_Giay").Trim());
			DaiChienHon_MoRa_PhoBan = int.Parse(Config.IniReadValue("GameServer", "DaiChienHon_MoRa_PhoBan").Trim());
			DCH_MoRa_Gio = int.Parse(Config.IniReadValue("GameServer", "DCH_MoRa_Gio").Trim());
			DCH_MoRa_Phut = int.Parse(Config.IniReadValue("GameServer", "DCH_MoRa_Phut").Trim());
			DCH_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "DCH_MoRa_Giay").Trim());
			ThoiGian_ChuanBi_DCH = int.Parse(Config.IniReadValue("GameServer", "ThoiGian_ChuanBi_DCH").Trim());
			TongThoiGian_DCH = int.Parse(Config.IniReadValue("GameServer", "TongThoiGian_DCH").Trim());
			CoHayKhong_MoRa_Event_Noel = int.Parse(Config.IniReadValue("GameServer", "CoHayKhong_MoRa_Event_Noel").Trim());
			Noel_MoRa_Gio = int.Parse(Config.IniReadValue("GameServer", "Noel_MoRa_Gio").Trim());
			Noel_MoRa_Phut = int.Parse(Config.IniReadValue("GameServer", "Noel_MoRa_Phut").Trim());
			Noel_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "Noel_MoRa_Giay").Trim());
			ID_Monster_Drop_Event_GiangSinh = (Config.IniReadValue("GameServer", "ID_Monster_Drop_Event_GiangSinh").Trim().Length == 0) ? ID_Monster_Drop_Event_GiangSinh : int.Parse(Config.IniReadValue("GameServer", "ID_Monster_Drop_Event_GiangSinh").Trim());
			PhanThuong_Drop_Event_GiangSinh = (Config.IniReadValue("GameServer", "PhanThuong_Drop_Event_GiangSinh").Trim().Length == 0) ? PhanThuong_Drop_Event_GiangSinh : int.Parse(Config.IniReadValue("GameServer", "PhanThuong_Drop_Event_GiangSinh").Trim());
			Event_KhuLuyenTap_MoRa = int.Parse(Config.IniReadValue("GameServer", "Event_KhuLuyenTap_MoRa").Trim());
			KhuLuyenTap_MoRa_Gio = int.Parse(Config.IniReadValue("GameServer", "KhuLuyenTap_MoRa_Gio").Trim());
			KhuLuyenTap_MoRa_Phut = int.Parse(Config.IniReadValue("GameServer", "KhuLuyenTap_MoRa_Phut").Trim());
			KhuLuyenTap_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "KhuLuyenTap_MoRa_Giay").Trim());
			ON_OFF_MoRa_Event_Boss_PK = int.Parse(Config.IniReadValue("GameServer", "ON_OFF_MoRa_Event_Boss_PK").Trim());
			Boss_PK_MoRa_Gio = int.Parse(Config.IniReadValue("GameServer", "Boss_PK_MoRa_Gio").Trim());
			Boss_PK_MoRa_Phut = int.Parse(Config.IniReadValue("GameServer", "Boss_PK_MoRa_Phut").Trim());
			Boss_PK_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "Boss_PK_MoRa_Giay").Trim());
			CoHayKhong_MoRa_Event_TetGiapThin = int.Parse(Config.IniReadValue("GameServer", "CoHayKhong_MoRa_Event_TetGiapThin").Trim());
			TetGiapThin_MoRa_Gio = int.Parse(Config.IniReadValue("GameServer", "TetGiapThin_MoRa_Gio").Trim());
			TetGiapThin_MoRa_Phut = int.Parse(Config.IniReadValue("GameServer", "TetGiapThin_MoRa_Phut").Trim());
			TetGiapThin_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "TetGiapThin_MoRa_Giay").Trim());
			ID_Monster_Drop_Event_TetGiapThin = (Config.IniReadValue("GameServer", "ID_Monster_Drop_Event_TetGiapThin").Trim().Length == 0) ? ID_Monster_Drop_Event_TetGiapThin : int.Parse(Config.IniReadValue("GameServer", "ID_Monster_Drop_Event_TetGiapThin").Trim());
			PhanThuong_Drop_Event_TetGiapThin = (Config.IniReadValue("GameServer", "PhanThuong_Drop_Event_TetGiapThin").Trim().Length == 0) ? PhanThuong_Drop_Event_TetGiapThin : int.Parse(Config.IniReadValue("GameServer", "PhanThuong_Drop_Event_TetGiapThin").Trim());
			PhanThuong_Drop_Event_TetGiapThin_2 = (Config.IniReadValue("GameServer", "PhanThuong_Drop_Event_TetGiapThin_2").Trim().Length == 0) ? PhanThuong_Drop_Event_TetGiapThin_2 : int.Parse(Config.IniReadValue("GameServer", "PhanThuong_Drop_Event_TetGiapThin_2").Trim());
			IsPetGainEXP = int.Parse(Config.IniReadValue("GameServer", "pet_gain_exp").Trim());
			Time_Exp_Pet_Hour = int.Parse(Config.IniReadValue("GameServer", "Time_Exp_Pet_Hour").Trim());
			Time_Add_Exp_Pet_Minute = int.Parse(Config.IniReadValue("GameServer", "Time_Add_Exp_Pet_Minute").Trim());
			CongThanhChien_CoMoRaHayKhong = int.Parse(Config.IniReadValue("GameServer", "CongThanhChien_CoMoRaHayKhong").Trim());
			CongThanhChien_MoRa_Gio = int.Parse(Config.IniReadValue("GameServer", "CongThanhChien_MoRa_Gio").Trim());
			CongThanhChien_MoRa_Phut = int.Parse(Config.IniReadValue("GameServer", "CongThanhChien_MoRa_Phut").Trim());
			CongThanhChien_MoRa_Giay = int.Parse(Config.IniReadValue("GameServer", "CongThanhChien_MoRa_Giay").Trim());
			CongThanhChien_TongThoiGian = int.Parse(Config.IniReadValue("GameServer", "CongThanhChien_TongThoiGian").Trim());
			CongThanhChien_ThoiGianChuanBi = int.Parse(Config.IniReadValue("GameServer", "CongThanhChien_ThoiGianChuanBi").Trim());
			AutGC = (Config.IniReadValue("GameServer", "AutGC").Trim().Length == 0) ? AutGC : int.Parse(Config.IniReadValue("GameServer", "AutGC").Trim());
			PacketTitle = (Config.IniReadValue("GameServer", "PacketTitle").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "PacketTitle").Trim()) : 0;
			Script = (Config.IniReadValue("GameServer", "Script").Trim().Length == 0) ? Script : int.Parse(Config.IniReadValue("GameServer", "Script").Trim());
			SuTuHong_Max_SoLuong = (Config.IniReadValue("GameServer", "SuTuHong_Max_SoLuong").Length == 0) ? 50 : int.Parse(Config.IniReadValue("GameServer", "SuTuHong_Max_SoLuong"));
			TiLe_HopThanh_DaTuLinh = (Config.IniReadValue("GameServer", "TiLe_HopThanh_DaTuLinh").Length == 0) ? TiLe_HopThanh_DaTuLinh : double.Parse(Config.IniReadValue("GameServer", "TiLe_HopThanh_DaTuLinh"));
			GioiHan_SoLuong_ThuocTinh_VuKhi_TrangBi = (Config.IniReadValue("GameServer", "GioiHan_SoLuong_ThuocTinh_VuKhi_TrangBi").Length == 0) ? GioiHan_SoLuong_ThuocTinh_VuKhi_TrangBi : double.Parse(Config.IniReadValue("GameServer", "GioiHan_SoLuong_ThuocTinh_VuKhi_TrangBi"));
			TyLe_HopThanh = (Config.IniReadValue("GameServer", "TyLe_HopThanh").Length == 0) ? TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "TyLe_HopThanh"));
			TyLe_CuongHoa = (Config.IniReadValue("GameServer", "TyLe_CuongHoa").Length == 0) ? TyLe_CuongHoa : double.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa"));
			GioiHan_CuongHoa_AoChoang = (Config.IniReadValue("GameServer", "GioiHan_CuongHoa_AoChoang").Length == 0) ? 50 : int.Parse(Config.IniReadValue("GameServer", "GioiHan_CuongHoa_AoChoang"));
			TyLe_TangCuong_AoChoang = (Config.IniReadValue("GameServer", "TyLe_TangCuong_AoChoang").Length == 0) ? TyLe_TangCuong_AoChoang : double.Parse(Config.IniReadValue("GameServer", "TyLe_TangCuong_AoChoang"));
			TyLe_NangCap_ThietBi = (Config.IniReadValue("GameServer", "TyLe_NangCap_ThietBi").Length == 0) ? TyLe_NangCap_ThietBi : double.Parse(Config.IniReadValue("GameServer", "TyLe_NangCap_ThietBi"));
			TyLe_NangCap_TrangSuc = (Config.IniReadValue("GameServer", "TyLe_NangCap_TrangSuc").Length == 0) ? TyLe_NangCap_TrangSuc : double.Parse(Config.IniReadValue("GameServer", "TyLe_NangCap_TrangSuc"));
			NguyenBao_HopThanh = (Config.IniReadValue("GameServer", "NguyenBao_HopThanh").Trim().Length == 0) ? NguyenBao_HopThanh : int.Parse(Config.IniReadValue("GameServer", "NguyenBao_HopThanh").Trim());
			Encrypt = (Config.IniReadValue("GameServer", "Encrypt").Trim().Length == 0) ? Encrypt : int.Parse(Config.IniReadValue("GameServer", "Encrypt").Trim());
			EncryptionKey = Config.IniReadValue("GameServer", "EncryptionKey").Trim();
			VersionVerificationTime = (Config.IniReadValue("GameServer", "VersionVerificationTime").Trim().Length == 0) ? VersionVerificationTime : int.Parse(Config.IniReadValue("GameServer", "VersionVerificationTime").Trim());
			VipThongBaoTrucTuyen = (Config.IniReadValue("GameServer", "VipThongBaoTrucTuyen").Trim().Length == 0) ? VipThongBaoTrucTuyen : int.Parse(Config.IniReadValue("GameServer", "VipThongBaoTrucTuyen").Trim());
			NoiDung_VipThongBaoTrucTuyen = (Config.IniReadValue("GameServer", "NoiDung_VipThongBaoTrucTuyen").Trim().Length == 0) ? NoiDung_VipThongBaoTrucTuyen : Config.IniReadValue("GameServer", "NoiDung_VipThongBaoTrucTuyen").Trim();
			VIP_BanDo = Config.IniReadValue("GameServer", "VIP_BanDo").Trim();
			ThongBaoTrucTuyen_BinhThuong = (Config.IniReadValue("GameServer", "ThongBaoTrucTuyen_BinhThuong").Trim().Length == 0) ? ThongBaoTrucTuyen_BinhThuong : int.Parse(Config.IniReadValue("GameServer", "ThongBaoTrucTuyen_BinhThuong").Trim());
			KiemTraVatPham_BatHopPhap = (Config.IniReadValue("GameServer", "KiemTraVatPham_BatHopPhap").Trim().Length == 0) ? KiemTraVatPham_BatHopPhap : int.Parse(Config.IniReadValue("GameServer", "KiemTraVatPham_BatHopPhap").Trim());
			KiemTraCacMuc_BatHopPhap = (Config.IniReadValue("GameServer", "KiemTraCacMuc_BatHopPhap").Trim().Length == 0) ? KiemTraCacMuc_BatHopPhap : (Config.IniReadValue("GameServer", "KiemTraCacMuc_BatHopPhap").Trim() == "1");
			KiemTraCacHoatDong_BatHopPhap = (Config.IniReadValue("GameServer", "KiemTraCacHoatDong_BatHopPhap").Trim().Length == 0) ? KiemTraCacHoatDong_BatHopPhap : int.Parse(Config.IniReadValue("GameServer", "KiemTraCacHoatDong_BatHopPhap").Trim());
			VatPhamCaoNhatCongKichGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatCongKichGiaTri").Trim().Length == 0) ? VatPhamCaoNhatCongKichGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatCongKichGiaTri").Trim());
			VatPhamCaoNhatPhongNguGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatPhongNguGiaTri").Trim().Length == 0) ? VatPhamCaoNhatPhongNguGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatPhongNguGiaTri").Trim());
			VatPhamCaoNhatHPGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatHPGiaTri").Trim().Length == 0) ? VatPhamCaoNhatHPGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatHPGiaTri").Trim());
			VatPhamCaoNhatNoiCongGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatNoiCongGiaTri").Trim().Length == 0) ? VatPhamCaoNhatNoiCongGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatNoiCongGiaTri").Trim());
			VatPhamCaoNhatTrungDichGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatTrungDichGiaTri").Trim().Length == 0) ? VatPhamCaoNhatTrungDichGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatTrungDichGiaTri").Trim());
			VatPhamCaoNhatNeTranhGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatNeTranhGiaTri").Trim().Length == 0) ? VatPhamCaoNhatNeTranhGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatNeTranhGiaTri").Trim());
			VatPhamCaoNhatCongKichVoCongGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatCongKichVoCongGiaTri").Trim().Length == 0) ? VatPhamCaoNhatCongKichVoCongGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatCongKichVoCongGiaTri").Trim());
			VatPhamCaoNhatKhiCongGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatKhiCongGiaTri").Trim().Length == 0) ? VatPhamCaoNhatKhiCongGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatKhiCongGiaTri").Trim());
			VatPhamCaoNhatHopThanhGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatHopThanhGiaTri").Trim().Length == 0) ? VatPhamCaoNhatHopThanhGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatHopThanhGiaTri").Trim());
			VatPhamCaoNhatPhuHonGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatPhuHonGiaTri").Trim().Length == 0) ? VatPhamCaoNhatPhuHonGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatPhuHonGiaTri").Trim());
			VatPhamCaoNhatPhongNguVoCongGiaTri = (Config.IniReadValue("GameServer", "VatPhamCaoNhatPhongNguVoCongGiaTri").Trim().Length == 0) ? VatPhamCaoNhatPhongNguVoCongGiaTri : int.Parse(Config.IniReadValue("GameServer", "VatPhamCaoNhatPhongNguVoCongGiaTri").Trim());
			TheLucChien_ThuongLoai = (Config.IniReadValue("GameServer", "TheLucChien_ThuongLoai").Trim().Length == 0) ? TheLucChien_ThuongLoai : int.Parse(Config.IniReadValue("GameServer", "TheLucChien_ThuongLoai").Trim());
			TheLucChien_ThuongSoLuong = (Config.IniReadValue("GameServer", "TheLucChien_ThuongSoLuong").Trim().Length == 0) ? TheLucChien_ThuongSoLuong : int.Parse(Config.IniReadValue("GameServer", "TheLucChien_ThuongSoLuong").Trim());
			TheLucChien_ThuongThuocTinh = Config.IniReadValue("GameServer", "TheLucChien_ThuongThuocTinh").Trim();
			TheLucChien_ThuongGoiVatPham = (Config.IniReadValue("GameServer", "TheLucChien_ThuongGoiVatPham").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "TheLucChien_ThuongGoiVatPham").Trim()) : 0;
			TheLucChien_ThuongVatPham = Config.IniReadValue("GameServer", "TheLucChien_ThuongVatPham").Trim();
			SqlJl = (Config.IniReadValue("GameServer", "SqlJl").Trim().Length == 0) ? SqlJl : Config.IniReadValue("GameServer", "SqlJl").Trim();
			AutomaticArchive = (Config.IniReadValue("GameServer", "AutomaticArchive").Trim().Length == 0) ? AutomaticArchive : int.Parse(Config.IniReadValue("GameServer", "AutomaticArchive").Trim());
			ItemRecord = (Config.IniReadValue("GameServer", "ItemRecord").Trim().Length == 0) ? ItemRecord : int.Parse(Config.IniReadValue("GameServer", "ItemRecord").Trim());
			LoginRecord = (Config.IniReadValue("GameServer", "LoginRecord").Trim().Length == 0) ? LoginRecord : int.Parse(Config.IniReadValue("GameServer", "LoginRecord").Trim());
			DropRecord = (Config.IniReadValue("GameServer", "DropRecord").Trim().Length == 0) ? DropRecord : int.Parse(Config.IniReadValue("GameServer", "DropRecord").Trim());
			DrugRecord = (Config.IniReadValue("GameServer", "DrugRecord").Trim().Length == 0) ? DrugRecord : int.Parse(Config.IniReadValue("GameServer", "DrugRecord").Trim());
			SyntheticRecord = (Config.IniReadValue("GameServer", "SyntheticRecord").Trim().Length == 0) ? SyntheticRecord : int.Parse(Config.IniReadValue("GameServer", "SyntheticRecord").Trim());
			StoreRecord = (Config.IniReadValue("GameServer", "StoreRecord").Trim().Length == 0) ? StoreRecord : int.Parse(Config.IniReadValue("GameServer", "StoreRecord").Trim());
			RecordKeepingDays = (Config.IniReadValue("GameServer", "RecordKeepingDays").Trim().Length == 0) ? RecordKeepingDays : int.Parse(Config.IniReadValue("GameServer", "RecordKeepingDays").Trim());
			KiemTra_NguyenBao = (Config.IniReadValue("GameServer", "KiemTra_NguyenBao").Trim().Length == 0) ? KiemTra_NguyenBao : int.Parse(Config.IniReadValue("GameServer", "KiemTra_NguyenBao").Trim());
			ThangThienKyNang_CapDoTangThem = (Config.IniReadValue("GameServer", "ThangThienKyNang_CapDoTangThem").Trim().Length == 0) ? 1 : int.Parse(Config.IniReadValue("GameServer", "ThangThienKyNang_CapDoTangThem").Trim());
			GioiHanCapDo_ToDoi = (Config.IniReadValue("GameServer", "GioiHanCapDo_ToDoi").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "GioiHanCapDo_ToDoi").Trim()) : 0;
			CoMo_TrucTuyen_ChoNguoiMoi_LamQuenHayKhong = (Config.IniReadValue("GameServer", "CoMo_TrucTuyen_ChoNguoiMoi_LamQuenHayKhong").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "CoMo_TrucTuyen_ChoNguoiMoi_LamQuenHayKhong").Trim()) : 0;
			CapDo_TrucTuyen = (Config.IniReadValue("GameServer", "CapDo_TrucTuyen").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "CapDo_TrucTuyen").Trim()) : 0;
			FirstLoginGift = (Config.IniReadValue("GameServer", "firstlogin_gift").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "firstlogin_gift").Trim()) : 0;
			Log_Hop_Code_Event = (Config.IniReadValue("GameServer", "Log_Hop_Code_Event").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "Log_Hop_Code_Event").Trim()) : 0;
			CoMoRaNganPhieu_DoiNguyenBaoHayKhong = (Config.IniReadValue("GameServer", "CoMoRaNganPhieu_DoiNguyenBaoHayKhong").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "CoMoRaNganPhieu_DoiNguyenBaoHayKhong").Trim()) : 0;
			NganPhieuDoiLayNguyenBao = (Config.IniReadValue("GameServer", "NganPhieuDoiLayNguyenBao").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "NganPhieuDoiLayNguyenBao").Trim()) : 0;
			PhaiChang_MoRaTreoMay_BanThuong = (Config.IniReadValue("GameServer", "PhaiChang_MoRaTreoMay_BanThuong").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "PhaiChang_MoRaTreoMay_BanThuong").Trim()) : 0;
			TreoMayBanThuong_ThoiGianChuKyLapLai = (Config.IniReadValue("GameServer", "TreoMayBanThuong_ThoiGianChuKyLapLai").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "TreoMayBanThuong_ThoiGianChuKyLapLai").Trim()) : 0;
			TreoMayBanThuong_YeuCauDangCap = (Config.IniReadValue("GameServer", "TreoMayBanThuong_YeuCauDangCap").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "TreoMayBanThuong_YeuCauDangCap").Trim()) : 0;
			TreoMayBanThuong_VoHuan = (Config.IniReadValue("GameServer", "TreoMayBanThuong_VoHuan").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "TreoMayBanThuong_VoHuan").Trim()) : 0;
			TreoMayBanThuong_VoHuan = (Config.IniReadValue("GameServer", "TreoMayBanThuong_VoHuan").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "TreoMayBanThuong_VoHuan").Trim()) : 0;
			Hut_Quai_SoLuong = (Config.IniReadValue("GameServer", "Hut_Quai_SoLuong").Trim().Length == 0) ? Hut_Quai_SoLuong : int.Parse(Config.IniReadValue("GameServer", "Hut_Quai_SoLuong").Trim());
			LayDuoc_KinhNghiem_CapDo_ChenhLech = (Config.IniReadValue("GameServer", "LayDuoc_KinhNghiem_CapDo_ChenhLech").Trim().Length == 0) ? 15 : int.Parse(Config.IniReadValue("GameServer", "LayDuoc_KinhNghiem_CapDo_ChenhLech").Trim());
			PhaiChang_MoRaThietBi_ThemChucNang_MoKhoaHayKhong = (Config.IniReadValue("GameServer", "PhaiChang_MoRaThietBi_ThemChucNang_MoKhoaHayKhong").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "PhaiChang_MoRaThietBi_ThemChucNang_MoKhoaHayKhong").Trim()) : 0;
			KhoaThietBi_TonNguyenBao = (Config.IniReadValue("GameServer", "KhoaThietBi_TonNguyenBao").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "KhoaThietBi_TonNguyenBao").Trim()) : 0;
			MoKhoaThietBi_TonNguyenBao = (Config.IniReadValue("GameServer", "MoKhoaThietBi_TonNguyenBao").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "MoKhoaThietBi_TonNguyenBao").Trim()) : 0;
			SoNguyenBao_ToiDa_TrongMotGiaoDich = (Config.IniReadValue("GameServer", "SoNguyenBao_ToiDa_TrongMotGiaoDich").Trim().Length == 0) ? SoNguyenBao_ToiDa_TrongMotGiaoDich : int.Parse(Config.IniReadValue("GameServer", "SoNguyenBao_ToiDa_TrongMotGiaoDich").Trim());
			GioiHan_TongSoNguyenBao_1TaiKhoan = (Config.IniReadValue("GameServer", "GioiHan_TongSoNguyenBao_1TaiKhoan").Trim().Length == 0) ? GioiHan_TongSoNguyenBao_1TaiKhoan : int.Parse(Config.IniReadValue("GameServer", "GioiHan_TongSoNguyenBao_1TaiKhoan").Trim());
			HoatDong_PhatHienPhoi = (Config.IniReadValue("GameServer", "HoatDong_PhatHienPhoi").Trim().Length == 0) ? HoatDong_PhatHienPhoi : int.Parse(Config.IniReadValue("GameServer", "HoatDong_PhatHienPhoi").Trim());
			PhaiChang_MoRaVoHuan_HeThong = (Config.IniReadValue("GameServer", "PhaiChang_MoRaVoHuan_HeThong").Trim().Length == 0) ? PhaiChang_MoRaVoHuan_HeThong : int.Parse(Config.IniReadValue("GameServer", "PhaiChang_MoRaVoHuan_HeThong").Trim());
			PKCapDo_ChenhLech = (Config.IniReadValue("GameServer", "PKCapDo_ChenhLech").Trim().Length == 0) ? PKCapDo_ChenhLech : int.Parse(Config.IniReadValue("GameServer", "PKCapDo_ChenhLech").Trim());
			VoHuanBaoVe_CapDo = (Config.IniReadValue("GameServer", "VoHuanBaoVe_CapDo").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "VoHuanBaoVe_CapDo").Trim()) : 0;
			TuVong_GiamBotVoHuan_SoLuong = Config.IniReadValue("GameServer", "TuVong_GiamBotVoHuan_SoLuong").Trim();
			HeThongTaiChe_SoLan = Config.IniReadValue("GameServer", "HeThongTaiChe_SoLan").Trim();
			TaiTao_DaKimCuong_CongKich = Config.IniReadValue("GameServer", "TaiTao_DaKimCuong_CongKich").Trim();
			TaiTao_DaKimCuong_TruyThuong = Config.IniReadValue("GameServer", "TaiTao_DaKimCuong_TruyThuong").Trim();
			TaiTao_DaKimCuong_VoCong = Config.IniReadValue("GameServer", "TaiTao_DaKimCuong_VoCong").Trim();
			TaiTao_DaKimCuong_TrungDich = Config.IniReadValue("GameServer", "TaiTao_DaKimCuong_TrungDich").Trim();
			TaiTao_DaKimCuong_SinhMenh = Config.IniReadValue("GameServer", "TaiTao_DaKimCuong_SinhMenh").Trim();
			TaiTao_HanNgocThach_PhongNgu = Config.IniReadValue("GameServer", "TaiTao_HanNgocThach_PhongNgu").Trim();
			TaiTao_HanNgocThach_NeTranh = Config.IniReadValue("GameServer", "TaiTao_HanNgocThach_NeTranh").Trim();
			TaiTao_HanNgocThach_SinhMenh = Config.IniReadValue("GameServer", "TaiTao_HanNgocThach_SinhMenh").Trim();
			TaiTao_HanNgocThach_NoiCong = Config.IniReadValue("GameServer", "TaiTao_HanNgocThach_NoiCong").Trim();
			TaiTao_HanNgocThach_VoPhong = Config.IniReadValue("GameServer", "TaiTao_HanNgocThach_VoPhong").Trim();
			Max_CuongHoa_ThucTinh = (Config.IniReadValue("GameServer", "Max_CuongHoa_ThucTinh").Trim().Length == 0) ? Max_CuongHoa_ThucTinh : int.Parse(Config.IniReadValue("GameServer", "Max_CuongHoa_ThucTinh").Trim());
			ThucTinh_VuKhi_TrangBi_Len_1 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_1") == "") ? ThucTinh_VuKhi_TrangBi_Len_1 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_1"));
			ThucTinh_VuKhi_TrangBi_Len_2 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_2") == "") ? ThucTinh_VuKhi_TrangBi_Len_2 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_2"));
			ThucTinh_VuKhi_TrangBi_Len_3 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_3") == "") ? ThucTinh_VuKhi_TrangBi_Len_3 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_3"));
			ThucTinh_VuKhi_TrangBi_Len_4 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_4") == "") ? ThucTinh_VuKhi_TrangBi_Len_4 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_4"));
			ThucTinh_VuKhi_TrangBi_Len_5 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_5") == "") ? ThucTinh_VuKhi_TrangBi_Len_5 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_5"));
			ThucTinh_VuKhi_TrangBi_Len_6 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_6") == "") ? ThucTinh_VuKhi_TrangBi_Len_6 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_6"));
			ThucTinh_VuKhi_TrangBi_Len_7 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_7") == "") ? ThucTinh_VuKhi_TrangBi_Len_7 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_7"));
			ThucTinh_VuKhi_TrangBi_Len_8 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_8") == "") ? ThucTinh_VuKhi_TrangBi_Len_8 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_8"));
			ThucTinh_VuKhi_TrangBi_Len_9 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_9") == "") ? ThucTinh_VuKhi_TrangBi_Len_9 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_9"));
			ThucTinh_VuKhi_TrangBi_Len_10 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_10") == "") ? ThucTinh_VuKhi_TrangBi_Len_10 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_10"));
			ThucTinh_VuKhi_TrangBi_Len_11 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_11") == "") ? ThucTinh_VuKhi_TrangBi_Len_11 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_11"));
			ThucTinh_VuKhi_TrangBi_Len_12 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_12") == "") ? ThucTinh_VuKhi_TrangBi_Len_12 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_12"));
			ThucTinh_VuKhi_TrangBi_Len_13 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_13") == "") ? ThucTinh_VuKhi_TrangBi_Len_13 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_13"));
			ThucTinh_VuKhi_TrangBi_Len_14 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_14") == "") ? ThucTinh_VuKhi_TrangBi_Len_14 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_14"));
			ThucTinh_VuKhi_TrangBi_Len_15 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_15") == "") ? ThucTinh_VuKhi_TrangBi_Len_15 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_15"));
			ThucTinh_VuKhi_TrangBi_Len_16 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_16") == "") ? ThucTinh_VuKhi_TrangBi_Len_16 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_16"));
			ThucTinh_VuKhi_TrangBi_Len_17 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_17") == "") ? ThucTinh_VuKhi_TrangBi_Len_17 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_17"));
			ThucTinh_VuKhi_TrangBi_Len_18 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_18") == "") ? ThucTinh_VuKhi_TrangBi_Len_18 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_18"));
			ThucTinh_VuKhi_TrangBi_Len_19 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_19") == "") ? ThucTinh_VuKhi_TrangBi_Len_19 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_19"));
			ThucTinh_VuKhi_TrangBi_Len_20 = (Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_20") == "") ? ThucTinh_VuKhi_TrangBi_Len_20 : double.Parse(Config.IniReadValue("GameServer", "ThucTinh_VuKhi_TrangBi_Len_20"));
			TyLe_HopThanh_1 = (Config.IniReadValue("GameServer", "TyLe_HopThanh_1") == "") ? TyLe_HopThanh_1 : double.Parse(Config.IniReadValue("GameServer", "TyLe_HopThanh_1"));
			TyLe_HopThanh_2 = (Config.IniReadValue("GameServer", "TyLe_HopThanh_2") == "") ? TyLe_HopThanh_2 : double.Parse(Config.IniReadValue("GameServer", "TyLe_HopThanh_2"));
			TyLe_HopThanh_3 = (Config.IniReadValue("GameServer", "TyLe_HopThanh_3") == "") ? TyLe_HopThanh_3 : double.Parse(Config.IniReadValue("GameServer", "TyLe_HopThanh_3"));
			TyLe_HopThanh_4 = (Config.IniReadValue("GameServer", "TyLe_HopThanh_4") == "") ? TyLe_HopThanh_4 : double.Parse(Config.IniReadValue("GameServer", "TyLe_HopThanh_4"));
			TyLe_HopThanh_HoaLongThach_dong_1 = (Config.IniReadValue("GameServer", "TyLe_HopThanh_HoaLongThach_dong_1") == "") ? TyLe_HopThanh_HoaLongThach_dong_1 : double.Parse(Config.IniReadValue("GameServer", "TyLe_HopThanh_HoaLongThach_dong_1"));
			TienHoa_ThanThu_1 = (Config.IniReadValue("GameServer", "TienHoa_ThanThu_1") == "") ? TienHoa_ThanThu_1 : double.Parse(Config.IniReadValue("GameServer", "TienHoa_ThanThu_1"));
			TienHoa_ThanThu_2 = (Config.IniReadValue("GameServer", "TienHoa_ThanThu_2") == "") ? TienHoa_ThanThu_2 : double.Parse(Config.IniReadValue("GameServer", "TienHoa_ThanThu_2"));
			TienHoa_ThanThu_3 = (Config.IniReadValue("GameServer", "TienHoa_ThanThu_3") == "") ? TienHoa_ThanThu_3 : double.Parse(Config.IniReadValue("GameServer", "TienHoa_ThanThu_3"));
			TienHoa_ThanThu_4 = (Config.IniReadValue("GameServer", "TienHoa_ThanThu_4") == "") ? TienHoa_ThanThu_4 : double.Parse(Config.IniReadValue("GameServer", "TienHoa_ThanThu_4"));
			TienHoa_ThanThu_5 = (Config.IniReadValue("GameServer", "TienHoa_ThanThu_5") == "") ? TienHoa_ThanThu_5 : double.Parse(Config.IniReadValue("GameServer", "TienHoa_ThanThu_5"));
			TienHoa_ThanThu_6 = (Config.IniReadValue("GameServer", "TienHoa_ThanThu_6") == "") ? TienHoa_ThanThu_6 : double.Parse(Config.IniReadValue("GameServer", "TienHoa_ThanThu_6"));
			TienHoa_ThanThu_7 = (Config.IniReadValue("GameServer", "TienHoa_ThanThu_7") == "") ? TienHoa_ThanThu_7 : double.Parse(Config.IniReadValue("GameServer", "TienHoa_ThanThu_7"));
			TienHoa_ThanThu_8 = (Config.IniReadValue("GameServer", "TienHoa_ThanThu_8") == "") ? TienHoa_ThanThu_8 : double.Parse(Config.IniReadValue("GameServer", "TienHoa_ThanThu_8"));
			CuongHoa_DoThan_1 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_1") == "") ? CuongHoa_DoThan_1 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_1"));
			CuongHoa_DoThan_2 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_2") == "") ? CuongHoa_DoThan_2 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_2"));
			CuongHoa_DoThan_3 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_3") == "") ? CuongHoa_DoThan_3 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_3"));
			CuongHoa_DoThan_4 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_4") == "") ? CuongHoa_DoThan_4 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_4"));
			CuongHoa_DoThan_5 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5") == "") ? CuongHoa_DoThan_5 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5"));
			CuongHoa_DoThan_5_VuKhi_NangCap_1 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_1") == "") ? CuongHoa_DoThan_5_VuKhi_NangCap_1 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_1"));
			CuongHoa_DoThan_5_VuKhi_NangCap_2 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_2") == "") ? CuongHoa_DoThan_5_VuKhi_NangCap_2 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_2"));
			CuongHoa_DoThan_5_VuKhi_NangCap_3 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_3") == "") ? CuongHoa_DoThan_5_VuKhi_NangCap_3 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_3"));
			CuongHoa_DoThan_5_VuKhi_NangCap_4 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_4") == "") ? CuongHoa_DoThan_5_VuKhi_NangCap_4 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_4"));
			CuongHoa_DoThan_5_VuKhi_NangCap_5 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_5") == "") ? CuongHoa_DoThan_5_VuKhi_NangCap_5 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_5"));
			CuongHoa_DoThan_5_VuKhi_NangCap_6 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_6") == "") ? CuongHoa_DoThan_5_VuKhi_NangCap_6 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_6"));
			CuongHoa_DoThan_5_VuKhi_NangCap_7 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_7") == "") ? CuongHoa_DoThan_5_VuKhi_NangCap_7 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_7"));
			CuongHoa_DoThan_5_VuKhi_NangCap_8 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_8") == "") ? CuongHoa_DoThan_5_VuKhi_NangCap_8 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_5_VuKhi_NangCap_8"));
			CuongHoa_DoThan_6 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_6") == "") ? CuongHoa_DoThan_6 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_6"));
			CuongHoa_DoThan_7 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_7") == "") ? CuongHoa_DoThan_7 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_7"));
			CuongHoa_DoThan_8 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_8") == "") ? CuongHoa_DoThan_8 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_8"));
			CuongHoa_DoThan_9 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_9") == "") ? CuongHoa_DoThan_9 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_9"));
			CuongHoa_DoThan_10 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_10") == "") ? CuongHoa_DoThan_10 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_10"));
			CuongHoa_DoThan_11 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_11") == "") ? CuongHoa_DoThan_11 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_11"));
			CuongHoa_DoThan_12 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_12") == "") ? CuongHoa_DoThan_12 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_12"));
			CuongHoa_DoThan_13 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_13") == "") ? CuongHoa_DoThan_13 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_13"));
			CuongHoa_DoThan_14 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_14") == "") ? CuongHoa_DoThan_14 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_14"));
			CuongHoa_DoThan_15 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_15") == "") ? CuongHoa_DoThan_15 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_15"));
			CuongHoa_DoThan_16 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_16") == "") ? CuongHoa_DoThan_16 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_16"));
			CuongHoa_DoThan_17 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_17") == "") ? CuongHoa_DoThan_17 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_17"));
			CuongHoa_DoThan_18 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_18") == "") ? CuongHoa_DoThan_18 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_18"));
			CuongHoa_DoThan_19 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_19") == "") ? CuongHoa_DoThan_19 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_19"));
			CuongHoa_DoThan_20 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_20") == "") ? CuongHoa_DoThan_20 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_20"));
			CuongHoa_DoThan_21 = (Config.IniReadValue("GameServer", "CuongHoa_DoThan_21") == "") ? CuongHoa_DoThan_21 : double.Parse(Config.IniReadValue("GameServer", "CuongHoa_DoThan_21"));
			Item_MuiTen_NangCap_1 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_1") == "") ? Item_MuiTen_NangCap_1 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_1"));
			Item_MuiTen_NangCap_2 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_2") == "") ? Item_MuiTen_NangCap_2 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_2"));
			Item_MuiTen_NangCap_3 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_3") == "") ? Item_MuiTen_NangCap_3 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_3"));
			Item_MuiTen_NangCap_4 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_4") == "") ? Item_MuiTen_NangCap_4 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_4"));
			Item_MuiTen_NangCap_5 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_5") == "") ? Item_MuiTen_NangCap_5 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_5"));
			Item_MuiTen_NangCap_6 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_6") == "") ? Item_MuiTen_NangCap_6 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_6"));
			Item_MuiTen_NangCap_7 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_7") == "") ? Item_MuiTen_NangCap_7 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_7"));
			Item_MuiTen_NangCap_8 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_8") == "") ? Item_MuiTen_NangCap_8 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_8"));
			Item_MuiTen_NangCap_9 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_9") == "") ? Item_MuiTen_NangCap_9 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_9"));
			Item_MuiTen_NangCap_10 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_10") == "") ? Item_MuiTen_NangCap_10 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_10"));
			Item_MuiTen_NangCap_11 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_11") == "") ? Item_MuiTen_NangCap_11 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_11"));
			Item_MuiTen_NangCap_12 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_12") == "") ? Item_MuiTen_NangCap_12 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_12"));
			Item_MuiTen_NangCap_13 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_13") == "") ? Item_MuiTen_NangCap_13 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_13"));
			Item_MuiTen_NangCap_14 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_14") == "") ? Item_MuiTen_NangCap_14 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_14"));
			Item_MuiTen_NangCap_15 = (Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_15") == "") ? Item_MuiTen_NangCap_15 : double.Parse(Config.IniReadValue("GameServer", "Item_MuiTen_NangCap_15"));
			Rate_Item_BoXanh_Len_BoHong = (Config.IniReadValue("GameServer", "Rate_Item_BoXanh_Len_BoHong") == "") ? Rate_Item_BoXanh_Len_BoHong : double.Parse(Config.IniReadValue("GameServer", "Rate_Item_BoXanh_Len_BoHong"));
			Rate_Item_BoHong_Len_QuaBanh = (Config.IniReadValue("GameServer", "Rate_Item_BoHong_Len_QuaBanh") == "") ? Rate_Item_BoHong_Len_QuaBanh : double.Parse(Config.IniReadValue("GameServer", "Rate_Item_BoHong_Len_QuaBanh"));
			Rate_Item_QuaBanh_Len_Chuot = (Config.IniReadValue("GameServer", "Rate_Item_QuaBanh_Len_Chuot") == "") ? Rate_Item_QuaBanh_Len_Chuot : double.Parse(Config.IniReadValue("GameServer", "Rate_Item_QuaBanh_Len_Chuot"));
			CuongHoa1TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa1TyLe_HopThanh") == "") ? CuongHoa1TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa1TyLe_HopThanh"));
			CuongHoa2TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa2TyLe_HopThanh") == "") ? CuongHoa2TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa2TyLe_HopThanh"));
			CuongHoa3TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa3TyLe_HopThanh") == "") ? CuongHoa3TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa3TyLe_HopThanh"));
			CuongHoa4TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa4TyLe_HopThanh") == "") ? CuongHoa4TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa4TyLe_HopThanh"));
			CuongHoa5TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa5TyLe_HopThanh") == "") ? CuongHoa5TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa5TyLe_HopThanh"));
			CuongHoa6TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa6TyLe_HopThanh") == "") ? CuongHoa6TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa6TyLe_HopThanh"));
			CuongHoa7TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa7TyLe_HopThanh") == "") ? CuongHoa7TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa7TyLe_HopThanh"));
			CuongHoa8TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa8TyLe_HopThanh") == "") ? CuongHoa8TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa8TyLe_HopThanh"));
			CuongHoa9TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa9TyLe_HopThanh") == "") ? CuongHoa9TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa9TyLe_HopThanh"));
			CuongHoa10TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa10TyLe_HopThanh") == "") ? CuongHoa10TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa10TyLe_HopThanh"));
			CuongHoa11TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa11TyLe_HopThanh") == "") ? CuongHoa11TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa11TyLe_HopThanh"));
			CuongHoa12TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa12TyLe_HopThanh") == "") ? CuongHoa12TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa12TyLe_HopThanh"));
			CuongHoa13TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa13TyLe_HopThanh") == "") ? CuongHoa13TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa13TyLe_HopThanh"));
			CuongHoa14TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa14TyLe_HopThanh") == "") ? CuongHoa14TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa14TyLe_HopThanh"));
			CuongHoa15TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa15TyLe_HopThanh") == "") ? CuongHoa15TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa15TyLe_HopThanh"));
			CuongHoa16TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa16TyLe_HopThanh") == "") ? CuongHoa16TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa16TyLe_HopThanh"));
			CuongHoa17TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa17TyLe_HopThanh") == "") ? CuongHoa17TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa17TyLe_HopThanh"));
			CuongHoa18TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa18TyLe_HopThanh") == "") ? CuongHoa18TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa18TyLe_HopThanh"));
			CuongHoa19TyLe_HopThanh = (Config.IniReadValue("GameServer", "CuongHoa19TyLe_HopThanh") == "") ? CuongHoa19TyLe_HopThanh : double.Parse(Config.IniReadValue("GameServer", "CuongHoa19TyLe_HopThanh"));
			CuongHoa_GioiHan_Item_NPC_DKT = (Config.IniReadValue("GameServer", "CuongHoa_GioiHan_Item_NPC_DKT").Trim().Length == 0) ? CuongHoa_GioiHan_Item_NPC_DKT : int.Parse(Config.IniReadValue("GameServer", "CuongHoa_GioiHan_Item_NPC_DKT").Trim());
			BatDau_VK_TB_KhongMat_CuongHoa_Tai_NPC_DKT = (Config.IniReadValue("GameServer", "BatDau_VK_TB_KhongMat_CuongHoa_Tai_NPC_DKT").Trim().Length == 0) ? BatDau_VK_TB_KhongMat_CuongHoa_Tai_NPC_DKT : int.Parse(Config.IniReadValue("GameServer", "BatDau_VK_TB_KhongMat_CuongHoa_Tai_NPC_DKT").Trim());
			TyLe_CuongHoa_ToiCao = (Config.IniReadValue("GameServer", "TyLe_CuongHoa_ToiCao").Length == 0) ? TyLe_CuongHoa_ToiCao : double.Parse(Config.IniReadValue("GameServer", "TyLe_CuongHoa_ToiCao"));
			BuaCuongHoa_TiLe1 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe1") == "") ? BuaCuongHoa_TiLe1 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe1"));
			BuaCuongHoa_TiLe2 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe2") == "") ? BuaCuongHoa_TiLe2 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe2"));
			BuaCuongHoa_TiLe3 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe3") == "") ? BuaCuongHoa_TiLe3 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe3"));
			BuaCuongHoa_TiLe4 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe4") == "") ? BuaCuongHoa_TiLe4 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe4"));
			BuaCuongHoa_TiLe5 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe5") == "") ? BuaCuongHoa_TiLe5 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe5"));
			BuaCuongHoa_TiLe6 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe6") == "") ? BuaCuongHoa_TiLe6 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe6"));
			BuaCuongHoa_TiLe7 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe7") == "") ? BuaCuongHoa_TiLe7 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe7"));
			BuaCuongHoa_TiLe8 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe8") == "") ? BuaCuongHoa_TiLe8 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe8"));
			BuaCuongHoa_TiLe9 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe9") == "") ? BuaCuongHoa_TiLe9 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe9"));
			BuaCuongHoa_TiLe10 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe10") == "") ? BuaCuongHoa_TiLe10 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe10"));
			BuaCuongHoa_TiLe11 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe11") == "") ? BuaCuongHoa_TiLe11 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe11"));
			BuaCuongHoa_TiLe12 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe12") == "") ? BuaCuongHoa_TiLe12 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe12"));
			BuaCuongHoa_TiLe13 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe13") == "") ? BuaCuongHoa_TiLe13 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe13"));
			BuaCuongHoa_TiLe14 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe14") == "") ? BuaCuongHoa_TiLe14 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe14"));
			BuaCuongHoa_TiLe15 = (Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe15") == "") ? BuaCuongHoa_TiLe15 : double.Parse(Config.IniReadValue("GameServer", "BuaCuongHoa_TiLe15"));
			Dame_Min_ThichKhach = (Config.IniReadValue("GameServer", "Dame_Min_ThichKhach") == "") ? Dame_Min_ThichKhach : double.Parse(Config.IniReadValue("GameServer", "Dame_Min_ThichKhach"));
			Dame_Min_CamSu = (Config.IniReadValue("GameServer", "Dame_Min_CamSu") == "") ? Dame_Min_CamSu : double.Parse(Config.IniReadValue("GameServer", "Dame_Min_CamSu"));
			Buff_DameTrain_CamSu_VeLai_ChiSo_Goc = (Config.IniReadValue("GameServer", "Buff_DameTrain_CamSu_VeLai_ChiSo_Goc") == "") ? Buff_DameTrain_CamSu_VeLai_ChiSo_Goc : double.Parse(Config.IniReadValue("GameServer", "Buff_DameTrain_CamSu_VeLai_ChiSo_Goc"));
			Dame_Min_MLC = (Config.IniReadValue("GameServer", "Dame_Min_MLC") == "") ? Dame_Min_MLC : double.Parse(Config.IniReadValue("GameServer", "Dame_Min_MLC"));
			Dame_Min_TH = (Config.IniReadValue("GameServer", "Dame_Min_MLC") == "") ? Dame_Min_TH : double.Parse(Config.IniReadValue("GameServer", "Dame_Min_TH"));
			Dame_Combo_DHL = (Config.IniReadValue("GameServer", "Dame_Combo_DHL") == "") ? Dame_Combo_DHL : double.Parse(Config.IniReadValue("GameServer", "Dame_Combo_DHL"));
			Giam_Dame_NinJa = (Config.IniReadValue("GameServer", "Giam_Dame_NinJa") == "") ? Giam_Dame_NinJa : double.Parse(Config.IniReadValue("GameServer", "Giam_Dame_NinJa"));
			TongXacSuat_CuongHoaThanThu = Config.IniReadValue("GameServer", "TongXacSuat_CuongHoaThanThu").Trim();
			MoiLanTaiTao_TieuHaoThietLap = (Config.IniReadValue("GameServer", "MoiLanTaiTao_TieuHaoThietLap").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "MoiLanTaiTao_TieuHaoThietLap").Trim()) : 0;
			MoiLan_TieuHaoSoLuong = (Config.IniReadValue("GameServer", "MoiLan_TieuHaoSoLuong").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "MoiLan_TieuHaoSoLuong").Trim()) : 0;
			var num = (Config.IniReadValue("GameServer", "TyLePhanTram_PhongNguVoCong").Trim().Length == 0) ? TyLePhanTram_PhongNguVoCong : double.Parse(Config.IniReadValue("GameServer", "TyLePhanTram_PhongNguVoCong").Trim());
			TyLePhanTram_CongKichVoCong = (Config.IniReadValue("GameServer", "TyLePhanTram_CongKichVoCong").Trim().Length == 0) ? TyLePhanTram_CongKichVoCong : double.Parse(Config.IniReadValue("GameServer", "TyLePhanTram_CongKichVoCong").Trim());
			if (TyLePhanTram_PhongNguVoCong != num)
			{
				TyLePhanTram_PhongNguVoCong = num;
				foreach (var value in allConnectedChars.Values)
				{
					value.CalculateCharacterEquipmentData();
				}
			}
			CongTac_PhatHienNhipTim = (Config.IniReadValue("GameServer", "CongTac_PhatHienNhipTim").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "CongTac_PhatHienNhipTim").Trim()) : 0;
			HoatDong_PhatHienNhipTim = (Config.IniReadValue("GameServer", "HoatDong_PhatHienNhipTim").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "HoatDong_PhatHienNhipTim").Trim()) : 0;
			NguongThoiGian_PhatHienNhipTim = (Config.IniReadValue("GameServer", "NguongThoiGian_PhatHienNhipTim").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "NguongThoiGian_PhatHienNhipTim").Trim()) : 0;
			ThoiLuong_PhatHienNhipTim = (Config.IniReadValue("GameServer", "ThoiLuong_PhatHienNhipTim").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "ThoiLuong_PhatHienNhipTim").Trim()) : 0;
			CheDo_AnToan_TieuThuNguyenBao = (Config.IniReadValue("GameServer", "CheDo_AnToan_TieuThuNguyenBao").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "CheDo_AnToan_TieuThuNguyenBao").Trim()) : 0;
			MoiLan_SuTuHong_TieuHaoNguyenBao = (Config.IniReadValue("GameServer", "MoiLan_SuTuHong_TieuHaoNguyenBao").Trim().Length == 0) ? MoiLan_SuTuHong_TieuHaoNguyenBao : int.Parse(Config.IniReadValue("GameServer", "MoiLan_SuTuHong_TieuHaoNguyenBao").Trim());
			ChoPhep_MoSoLuong_NhieuHon = (Config.IniReadValue("GameServer", "ChoPhep_MoSoLuong_NhieuHon").Trim().Length == 0) ? ChoPhep_MoSoLuong_NhieuHon : int.Parse(Config.IniReadValue("GameServer", "ChoPhep_MoSoLuong_NhieuHon").Trim());
			ChoPhep_MoSoLuong_NhieuHon_Mac_Address = (Config.IniReadValue("GameServer", "ChoPhep_MoSoLuong_NhieuHon_Mac_Address").Trim().Length == 0) ? ChoPhep_MoSoLuong_NhieuHon_Mac_Address : int.Parse(Config.IniReadValue("GameServer", "ChoPhep_MoSoLuong_NhieuHon_Mac_Address").Trim());
			Gioi_han_so_nguoi_vao_party = (Config.IniReadValue("GameServer", "Gioi_han_so_nguoi_vao_party").Trim().Length == 0) ? Gioi_han_so_nguoi_vao_party : int.Parse(Config.IniReadValue("GameServer", "Gioi_han_so_nguoi_vao_party").Trim());
			SoNguoiDangKyCongThanhChien = (Config.IniReadValue("GameServer", "SoNguoiDangKyCongThanhChien").Trim().Length == 0) ? SoNguoiDangKyCongThanhChien : int.Parse(Config.IniReadValue("GameServer", "SoNguoiDangKyCongThanhChien").Trim());
			Attack_Find_doi_phuong = (Config.IniReadValue("GameServer", "Attack_Find_doi_phuong").Trim().Length == 0) ? Attack_Find_doi_phuong : int.Parse(Config.IniReadValue("GameServer", "Attack_Find_doi_phuong").Trim());
			Def_Find_doi_phuong = (Config.IniReadValue("GameServer", "Def_Find_doi_phuong").Trim().Length == 0) ? Def_Find_doi_phuong : int.Parse(Config.IniReadValue("GameServer", "Def_Find_doi_phuong").Trim());
			SoLuong_NguyenBao_MoiLanTieuThu = (Config.IniReadValue("GameServer", "SoLuong_NguyenBao_MoiLanTieuThu").Trim().Length == 0) ? SoLuong_NguyenBao_MoiLanTieuThu : int.Parse(Config.IniReadValue("GameServer", "SoLuong_NguyenBao_MoiLanTieuThu").Trim());
			text = "PhiVaoCua_ToiThieu";
			PhiVaoCua_ToiThieu = (Config.IniReadValue("GameServer", "PhiVaoCua_ToiThieu").Trim().Length == 0) ? PhiVaoCua_ToiThieu : int.Parse(Config.IniReadValue("GameServer", "PhiVaoCua_ToiThieu").Trim());
			text = "SanTapTienThue_TiLePhanTram";
			SanTapTienThue_TiLePhanTram = (Config.IniReadValue("GameServer", "SanTapTienThue_TiLePhanTram").Trim().Length == 0) ? SanTapTienThue_TiLePhanTram : double.Parse(Config.IniReadValue("GameServer", "SanTapTienThue_TiLePhanTram").Trim());
			text = "SanTapPhamVi_HieuQua";
			SanTapPhamVi_HieuQua = (Config.IniReadValue("GameServer", "SanTapPhamVi_HieuQua").Trim().Length == 0) ? SanTapPhamVi_HieuQua : float.Parse(Config.IniReadValue("GameServer", "SanTapPhamVi_HieuQua").Trim());
			text = "NguyenBaoBiTru_SauKhiTruDiem";
			NguyenBaoBiTru_SauKhiTruDiem = (Config.IniReadValue("GameServer", "NguyenBaoBiTru_SauKhiTruDiem").Trim().Length == 0) ? NguyenBaoBiTru_SauKhiTruDiem : int.Parse(Config.IniReadValue("GameServer", "NguyenBaoBiTru_SauKhiTruDiem").Trim());
			text = "TienBiTru_SauKhiTruDiem";
			TienBiTru_SauKhiTruDiem = (Config.IniReadValue("GameServer", "TienBiTru_SauKhiTruDiem").Trim().Length == 0) ? TienBiTru_SauKhiTruDiem : int.Parse(Config.IniReadValue("GameServer", "TienBiTru_SauKhiTruDiem").Trim());
			text = "SoLuongChoPhep_NguoiChoiDatCuoc";
			SoLuongChoPhep_NguoiChoiDatCuoc = (Config.IniReadValue("GameServer", "SoLuongChoPhep_NguoiChoiDatCuoc").Trim().Length == 0) ? SoLuongChoPhep_NguoiChoiDatCuoc : int.Parse(Config.IniReadValue("GameServer", "SoLuongChoPhep_NguoiChoiDatCuoc").Trim());
			text = "BanDoKhoa_Chat";
			text = "GiaTri_Gold_AutoBan";
			GiaTri_Gold_AutoBan = (Config.IniReadValue("GameServer", "GiaTri_Gold_AutoBan").Trim().Length == 0) ? GiaTri_Gold_AutoBan : int.Parse(Config.IniReadValue("GameServer", "GiaTri_Gold_AutoBan").Trim());
			BanDoKhoa_Chat = Config.IniReadValue("GameServer", "BanDoKhoa_Chat").Trim();
			text = "HanChe_CapDo_Nhom";
			HanChe_CapDo_Nhom = (Config.IniReadValue("GameServer", "HanChe_CapDo_Nhom").Trim().Length == 0) ? HanChe_CapDo_Nhom : int.Parse(Config.IniReadValue("GameServer", "HanChe_CapDo_Nhom").Trim());
			text = "ChetCoMatKinhNghiemKhong";
			ChetCoMatKinhNghiemKhong = (Config.IniReadValue("GameServer", "ChetCoMatKinhNghiemKhong").Trim().Length == 0) ? ChetCoMatKinhNghiemKhong : int.Parse(Config.IniReadValue("GameServer", "ChetCoMatKinhNghiemKhong").Trim());
			text = "ThanNuPK_KhoangCach";
			ThanNuPK_KhoangCach = (Config.IniReadValue("GameServer", "ThanNuPK_KhoangCach").Trim().Length == 0) ? ThanNuPK_KhoangCach : double.Parse(Config.IniReadValue("GameServer", "ThanNuPK_KhoangCach").Trim());
			text = "TuHaoPK_KhoangCach";
			TuHaoPK_KhoangCach = (Config.IniReadValue("GameServer", "TuHaoPK_KhoangCach").Trim().Length == 0) ? TuHaoPK_KhoangCach : double.Parse(Config.IniReadValue("GameServer", "TuHaoPK_KhoangCach").Trim());
			text = "CamSuPK_KhoangCach";
			CamSuPK_KhoangCach = (Config.IniReadValue("GameServer", "CamSuPK_KhoangCach").Trim().Length == 0) ? CamSuPK_KhoangCach : double.Parse(Config.IniReadValue("GameServer", "CamSuPK_KhoangCach").Trim());
			text = "MaiLieuChanPK_KhoangCach";
			MaiLieuChanPK_KhoangCach = (Config.IniReadValue("GameServer", "MaiLieuChanPK_KhoangCach").Trim().Length == 0) ? MaiLieuChanPK_KhoangCach : double.Parse(Config.IniReadValue("GameServer", "MaiLieuChanPK_KhoangCach").Trim());
			text = "DaiPhuPK_KhoangCach";
			DaiPhuPK_KhoangCach = (Config.IniReadValue("GameServer", "DaiPhuPK_KhoangCach").Trim().Length == 0) ? DaiPhuPK_KhoangCach : double.Parse(Config.IniReadValue("GameServer", "DaiPhuPK_KhoangCach").Trim());
			text = "CungTienPK_KhoangCach";
			CungTienPK_KhoangCach = (Config.IniReadValue("GameServer", "CungTienPK_KhoangCach").Trim().Length == 0) ? CungTienPK_KhoangCach : double.Parse(Config.IniReadValue("GameServer", "CungTienPK_KhoangCach").Trim());
			text = "NhungNgheNghiepKhac_CongKichKhoangCach";
			NhungNgheNghiepKhac_CongKichKhoangCach = (Config.IniReadValue("GameServer", "NhungNgheNghiepKhac_CongKichKhoangCach").Trim().Length == 0) ? NhungNgheNghiepKhac_CongKichKhoangCach : double.Parse(Config.IniReadValue("GameServer", "NhungNgheNghiepKhac_CongKichKhoangCach").Trim());
			text = "DaiPhu_DanhQuaiKhoangCach";
			DaiPhu_DanhQuaiKhoangCach = (Config.IniReadValue("GameServer", "DaiPhu_DanhQuaiKhoangCach").Trim().Length == 0) ? DaiPhu_DanhQuaiKhoangCach : double.Parse(Config.IniReadValue("GameServer", "DaiPhu_DanhQuaiKhoangCach").Trim());
			text = "CungTienPK_KhoangCach";
			CungTien_DanhQuaiKhoangCach = (Config.IniReadValue("GameServer", "CungTien_DanhQuaiKhoangCach").Trim().Length == 0) ? CungTien_DanhQuaiKhoangCach : double.Parse(Config.IniReadValue("GameServer", "CungTien_DanhQuaiKhoangCach").Trim());
			text = "NhungNgheNghiepKhac_CongKichKhoangCach";
			NhungNgheNghiepKhac_DanhQuaiKhoangCach = (Config.IniReadValue("GameServer", "NhungNgheNghiepKhac_DanhQuaiKhoangCach").Trim().Length == 0) ? NhungNgheNghiepKhac_DanhQuaiKhoangCach : double.Parse(Config.IniReadValue("GameServer", "NhungNgheNghiepKhac_DanhQuaiKhoangCach").Trim());
			text = "PhanTram_GiamXuong_TienNhanDuoc";
			PhanTram_GiamXuong_TienNhanDuoc = (Config.IniReadValue("GameServer", "PhanTram_GiamXuong_TienNhanDuoc").Trim().Length == 0) ? PhanTram_GiamXuong_TienNhanDuoc : double.Parse(Config.IniReadValue("GameServer", "PhanTram_GiamXuong_TienNhanDuoc").Trim());
			Gold_DoiTheLuc_qua_TaPhai = (Config.IniReadValue("GameServer", "Gold_DoiTheLuc_qua_TaPhai").Trim().Length == 0) ? Gold_DoiTheLuc_qua_TaPhai : int.Parse(Config.IniReadValue("GameServer", "Gold_DoiTheLuc_qua_TaPhai").Trim());
			text = "Gold_DoiTheLuc_qua_TaPhai";
			Gold_DoiTheLuc_qua_ChinhPhai = (Config.IniReadValue("GameServer", "Gold_DoiTheLuc_qua_ChinhPhai").Trim().Length == 0) ? Gold_DoiTheLuc_qua_ChinhPhai : int.Parse(Config.IniReadValue("GameServer", "Gold_DoiTheLuc_qua_ChinhPhai").Trim());
			text = "Gold_DoiTheLuc_qua_ChinhPhai";
			Phi_Doi_Character = (Config.IniReadValue("GameServer", "Phi_Doi_Character").Trim().Length == 0) ? Phi_Doi_Character : int.Parse(Config.IniReadValue("GameServer", "Phi_Doi_Character").Trim());
			text = "Phi_Doi_Character";
			Win_TLC_Phan_Thuong_VoHuan = (Config.IniReadValue("GameServer", "Win_TLC_Phan_Thuong_VoHuan").Trim().Length == 0) ? Win_TLC_Phan_Thuong_VoHuan : int.Parse(Config.IniReadValue("GameServer", "Win_TLC_Phan_Thuong_VoHuan").Trim());
			text = "Win_TLC_Phan_Thuong_VoHuan";
			Lose_TLC_Phan_Thuong_VoHuan = (Config.IniReadValue("GameServer", "Lose_TLC_Phan_Thuong_VoHuan").Trim().Length == 0) ? Lose_TLC_Phan_Thuong_VoHuan : int.Parse(Config.IniReadValue("GameServer", "Lose_TLC_Phan_Thuong_VoHuan").Trim());
			text = "Lose_TLC_Phan_Thuong_VoHuan";
			Win_DCH_Phan_Thuong_VoHuan = (Config.IniReadValue("GameServer", "Win_DCH_Phan_Thuong_VoHuan").Trim().Length == 0) ? Win_DCH_Phan_Thuong_VoHuan : int.Parse(Config.IniReadValue("GameServer", "Win_DCH_Phan_Thuong_VoHuan").Trim());
			text = "Win_DCH_Phan_Thuong_VoHuan";
			Lose_DCH_Phan_Thuong_VoHuan = (Config.IniReadValue("GameServer", "Lose_DCH_Phan_Thuong_VoHuan").Trim().Length == 0) ? Lose_DCH_Phan_Thuong_VoHuan : int.Parse(Config.IniReadValue("GameServer", "Lose_DCH_Phan_Thuong_VoHuan").Trim());
			text = "Lose_DCH_Phan_Thuong_VoHuan";
			CoMoRa_HeThong_MonChien = (Config.IniReadValue("GameServer", "CoMoRa_HeThong_MonChien").Trim().Length == 0) ? CoMoRa_HeThong_MonChien : int.Parse(Config.IniReadValue("GameServer", "CoMoRa_HeThong_MonChien").Trim());
			HeThong_MonChien_Gio = (Config.IniReadValue("GameServer", "HeThong_MonChien_Gio").Trim().Length == 0) ? HeThong_MonChien_Gio : int.Parse(Config.IniReadValue("GameServer", "HeThong_MonChien_Gio").Trim());
			HeThong_MonChien_Phut = (Config.IniReadValue("GameServer", "HeThong_MonChien_Phut").Trim().Length == 0) ? HeThong_MonChien_Phut : int.Parse(Config.IniReadValue("GameServer", "HeThong_MonChien_Phut").Trim());
			HeThong_MonChien_Giay = (Config.IniReadValue("GameServer", "HeThong_MonChien_Giay").Trim().Length == 0) ? HeThong_MonChien_Giay : int.Parse(Config.IniReadValue("GameServer", "HeThong_MonChien_Giay").Trim());
			HeThong_MonChien_CanNguyenBao = (Config.IniReadValue("GameServer", "HeThong_MonChien_CanNguyenBao").Trim().Length == 0) ? HeThong_MonChien_CanNguyenBao : int.Parse(Config.IniReadValue("GameServer", "HeThong_MonChien_CanNguyenBao").Trim());
			X2KinhNghiem_CapDo_GioiHanCaoNhat = (Config.IniReadValue("GameServer", "X2KinhNghiem_CapDo_GioiHanCaoNhat").Trim().Length == 0) ? X2KinhNghiem_CapDo_GioiHanCaoNhat : int.Parse(Config.IniReadValue("GameServer", "X2KinhNghiem_CapDo_GioiHanCaoNhat").Trim());
			X2TienTai_CapDo_GioiHanCaoNhat = (Config.IniReadValue("GameServer", "X2TienTai_CapDo_GioiHanCaoNhat").Trim().Length == 0) ? X2TienTai_CapDo_GioiHanCaoNhat : int.Parse(Config.IniReadValue("GameServer", "X2TienTai_CapDo_GioiHanCaoNhat").Trim());
			X2LichLuyen_CapDo_GioiHanCaoNhat = (Config.IniReadValue("GameServer", "X2LichLuyen_CapDo_GioiHanCaoNhat").Trim().Length == 0) ? X2LichLuyen_CapDo_GioiHanCaoNhat : int.Parse(Config.IniReadValue("GameServer", "X2LichLuyen_CapDo_GioiHanCaoNhat").Trim());
			X2BaoSuat_CapDo_GioiHanCaoNhat = (Config.IniReadValue("GameServer", "X2BaoSuat_CapDo_GioiHanCaoNhat").Trim().Length == 0) ? X2BaoSuat_CapDo_GioiHanCaoNhat : int.Parse(Config.IniReadValue("GameServer", "X2BaoSuat_CapDo_GioiHanCaoNhat").Trim());
			X2CapDo_GioiHanCaoNhat_BoiSo = (Config.IniReadValue("GameServer", "X2CapDo_GioiHanCaoNhat_BoiSo").Trim().Length == 0) ? X2CapDo_GioiHanCaoNhat_BoiSo : double.Parse(Config.IniReadValue("GameServer", "X2CapDo_GioiHanCaoNhat_BoiSo").Trim());
			Kenh_MoHop_Event = (Config.IniReadValue("GameServer", "Kenh_MoHop_Event").Trim().Length == 0) ? Kenh_MoHop_Event : int.Parse(Config.IniReadValue("GameServer", "Kenh_MoHop_Event").Trim());
			Kenh_Treo_Shop = (Config.IniReadValue("GameServer", "Kenh_Treo_Shop").Trim().Length == 0) ? Kenh_Treo_Shop : int.Parse(Config.IniReadValue("GameServer", "Kenh_Treo_Shop").Trim());
			GioiHan_Level_CaoNhat = (Config.IniReadValue("GameServer", "GioiHan_Level_CaoNhat").Trim().Length == 0) ? GioiHan_Level_CaoNhat : int.Parse(Config.IniReadValue("GameServer", "GioiHan_Level_CaoNhat").Trim());
			DiDong_TocDo = Config.IniReadValue("GameServer", "TocDoDiChuyen_Max").Trim().Split(';');
			GioiHan_CuongHoa_ThanThu = (Config.IniReadValue("GameServer", "GioiHan_CuongHoa_ThanThu").Trim().Length == 0) ? GioiHan_CuongHoa_ThanThu : int.Parse(Config.IniReadValue("GameServer", "GioiHan_CuongHoa_ThanThu").Trim());
			ON_OFF_LogChangeSkill = (Config.IniReadValue("GameServer", "ON_OFF_LogChangeSkill").Trim().Length == 0) ? ON_OFF_LogChangeSkill : int.Parse(Config.IniReadValue("GameServer", "ON_OFF_LogChangeSkill").Trim());
			XacNhan_Dame_Train_Client_den_Server = (Config.IniReadValue("GameServer", "XacNhan_Dame_Train_Client_den_Server").Trim().Length == 0) ? XacNhan_Dame_Train_Client_den_Server : int.Parse(Config.IniReadValue("GameServer", "XacNhan_Dame_Train_Client_den_Server").Trim());
			Event_MoHop_SoLuong = (Config.IniReadValue("GameServer", "Event_MoHop_SoLuong").Trim().Length == 0) ? Event_MoHop_SoLuong : int.Parse(Config.IniReadValue("GameServer", "Event_MoHop_SoLuong").Trim());
			Test_Packet_Bytes_1 = (Config.IniReadValue("GameServer", "Test_Packet_Bytes_1").Trim().Length == 0) ? Test_Packet_Bytes_1 : int.Parse(Config.IniReadValue("GameServer", "Test_Packet_Bytes_1").Trim());
			Test_Packet_Bytes_2 = (Config.IniReadValue("GameServer", "Test_Packet_Bytes_2").Trim().Length == 0) ? Test_Packet_Bytes_2 : int.Parse(Config.IniReadValue("GameServer", "Test_Packet_Bytes_2").Trim());
			Test_Packet_Bytes_3 = (Config.IniReadValue("GameServer", "Test_Packet_Bytes_3").Trim().Length == 0) ? Test_Packet_Bytes_3 : int.Parse(Config.IniReadValue("GameServer", "Test_Packet_Bytes_3").Trim());
			Test_Packet_Bytes_4 = (Config.IniReadValue("GameServer", "Test_Packet_Bytes_4").Trim().Length == 0) ? Test_Packet_Bytes_4 : int.Parse(Config.IniReadValue("GameServer", "Test_Packet_Bytes_4").Trim());
			Random_TamHoaTuDinh_Chinh = (Config.IniReadValue("GameServer", "Random_TamHoaTuDinh_Chinh").Trim().Length == 0) ? Random_TamHoaTuDinh_Chinh : int.Parse(Config.IniReadValue("GameServer", "Random_TamHoaTuDinh_Chinh").Trim());
			Random_TamHoaTuDinh_Ta = (Config.IniReadValue("GameServer", "Random_TamHoaTuDinh_Ta").Trim().Length == 0) ? Random_TamHoaTuDinh_Ta : int.Parse(Config.IniReadValue("GameServer", "Random_TamHoaTuDinh_Ta").Trim());
			Min_Delay_Skill_Monster = (Config.IniReadValue("GameServer", "Min_Delay_Skill_Monster").Trim().Length == 0) ? Min_Delay_Skill_Monster : int.Parse(Config.IniReadValue("GameServer", "Min_Delay_Skill_Monster").Trim());
			Max_Delay_Skill_Monster = (Config.IniReadValue("GameServer", "Max_Delay_Skill_Monster").Trim().Length == 0) ? Max_Delay_Skill_Monster : int.Parse(Config.IniReadValue("GameServer", "Max_Delay_Skill_Monster").Trim());
			Giam_Delay_Skill_CharDao_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDao_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharDao_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDao_PK").Trim());
			Giam_Delay_Skill_CharKiem_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharKiem_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharKiem_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharKiem_PK").Trim());
			Giam_Delay_Skill_CharThuong_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharThuong_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharThuong_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharThuong_PK").Trim());
			Giam_Delay_Skill_CharCung_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharCung_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharCung_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharCung_PK").Trim());
			Giam_Delay_Skill_CharDP_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDP_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharDP_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDP_PK").Trim());
			Giam_Delay_Skill_CharTK_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTK_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharTK_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTK_PK").Trim());
			Giam_Delay_Skill_CharCS_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharCS_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharCS_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharCS_PK").Trim());
			Giam_Delay_Skill_CharHBQ_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharHBQ_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharHBQ_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharHBQ_PK").Trim());
			Giam_Delay_Skill_CharDHL_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDHL_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharDHL_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDHL_PK").Trim());
			Giam_Delay_Skill_CharQS_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharQS_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharQS_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharQS_PK").Trim());
			Giam_Delay_Skill_CharMLC_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharMLC_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharMLC_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharMLC_PK").Trim());
			Giam_Delay_Skill_CharTH_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTH_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharTH_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTH_PK").Trim());
			Giam_Delay_Skill_CharTN_PK = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTN_PK").Trim().Length == 0) ? Giam_Delay_Skill_CharTN_PK : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTN_PK").Trim());
			Giam_Delay_Skill_CharDao_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDao_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharDao_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDao_Train").Trim());
			Giam_Delay_Skill_CharKiem_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharKiem_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharKiem_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharKiem_Train").Trim());
			Giam_Delay_Skill_CharThuong_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharThuong_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharThuong_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharThuong_Train").Trim());
			Giam_Delay_Skill_CharCung_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharCung_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharCung_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharCung_Train").Trim());
			Giam_Delay_Skill_CharDP_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDP_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharDP_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDP_Train").Trim());
			Giam_Delay_Skill_CharTK_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTK_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharTK_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTK_Train").Trim());
			Giam_Delay_Skill_CharCS_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharCS_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharCS_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharCS_Train").Trim());
			Giam_Delay_Skill_CharHBQ_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharHBQ_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharHBQ_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharHBQ_Train").Trim());
			Giam_Delay_Skill_CharDHL_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDHL_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharDHL_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharDHL_Train").Trim());
			Giam_Delay_Skill_CharQS_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharQS_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharQS_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharQS_Train").Trim());
			Giam_Delay_Skill_CharMLC_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharMLC_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharMLC_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharMLC_Train").Trim());
			Giam_Delay_Skill_CharTH_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTH_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharTH_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTH_Train").Trim());
			Giam_Delay_Skill_CharTN_Train = (Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTN_Train").Trim().Length == 0) ? Giam_Delay_Skill_CharTN_Train : int.Parse(Config.IniReadValue("GameServer", "Giam_Delay_Skill_CharTN_Train").Trim());
			Random_Type_Box_Item = (Config.IniReadValue("GameServer", "Random_Type_Box_Item").Trim().Length == 0) ? Random_Type_Box_Item : int.Parse(Config.IniReadValue("GameServer", "Random_Type_Box_Item").Trim());
			CoHayKo_KichHoat_TaiKhoan_VaoDuocGame = (Config.IniReadValue("GameServer", "CoHayKo_KichHoat_TaiKhoan_VaoDuocGame").Trim().Length == 0) ? CoHayKo_KichHoat_TaiKhoan_VaoDuocGame : int.Parse(Config.IniReadValue("GameServer", "CoHayKo_KichHoat_TaiKhoan_VaoDuocGame").Trim());
			Thuong_ON_CPVP_NhanThemSatThuong = (Config.IniReadValue("GameServer", "Thuong_ON_CPVP_NhanThemSatThuong").Trim().Length == 0) ? Thuong_ON_CPVP_NhanThemSatThuong : double.Parse(Config.IniReadValue("GameServer", "Thuong_ON_CPVP_NhanThemSatThuong").Trim());
			Thuong_OFF_CPVP_NhanThemSatThuong = (Config.IniReadValue("GameServer", "Thuong_OFF_CPVP_NhanThemSatThuong").Trim().Length == 0) ? Thuong_OFF_CPVP_NhanThemSatThuong : double.Parse(Config.IniReadValue("GameServer", "Thuong_OFF_CPVP_NhanThemSatThuong").Trim());
			Quyen_ON_CPVP_NhanThemSatThuong = (Config.IniReadValue("GameServer", "Quyen_ON_CPVP_NhanThemSatThuong").Trim().Length == 0) ? Quyen_ON_CPVP_NhanThemSatThuong : double.Parse(Config.IniReadValue("GameServer", "Quyen_ON_CPVP_NhanThemSatThuong").Trim());
			Quyen_OFF_CPVP_NhanThemSatThuong = (Config.IniReadValue("GameServer", "Quyen_OFF_CPVP_NhanThemSatThuong").Trim().Length == 0) ? Quyen_OFF_CPVP_NhanThemSatThuong : double.Parse(Config.IniReadValue("GameServer", "Quyen_OFF_CPVP_NhanThemSatThuong").Trim());
			Random_Rate_KinhNghiem = (Config.IniReadValue("GameServer", "Random_Rate_KinhNghiem").Trim().Length == 0) ? Random_Rate_KinhNghiem : int.Parse(Config.IniReadValue("GameServer", "Random_Rate_KinhNghiem").Trim());
			Random_Rate_Gold = (Config.IniReadValue("GameServer", "Random_Rate_Gold").Trim().Length == 0) ? Random_Rate_Gold : int.Parse(Config.IniReadValue("GameServer", "Random_Rate_Gold").Trim());
			Random_Rate_KyNang = (Config.IniReadValue("GameServer", "Random_Rate_KyNang").Trim().Length == 0) ? Random_Rate_KyNang : int.Parse(Config.IniReadValue("GameServer", "Random_Rate_KyNang").Trim());
			Random_Rate_KyNangThangThien = (Config.IniReadValue("GameServer", "Random_Rate_KyNangThangThien").Trim().Length == 0) ? Random_Rate_KyNangThangThien : int.Parse(Config.IniReadValue("GameServer", "Random_Rate_KyNangThangThien").Trim());
			Time_SK1_Char_Cung = (Config.IniReadValue("GameServer", "Time_SK1_Char_Cung").Trim().Length == 0) ? Time_SK1_Char_Cung : int.Parse(Config.IniReadValue("GameServer", "Time_SK1_Char_Cung").Trim());
			Time_SK1_Char_DHL = (Config.IniReadValue("GameServer", "Time_SK1_Char_DHL").Trim().Length == 0) ? Time_SK1_Char_DHL : int.Parse(Config.IniReadValue("GameServer", "Time_SK1_Char_DHL").Trim());
			Time_SK1_Char_ConLai = (Config.IniReadValue("GameServer", "Time_SK1_Char_ConLai").Trim().Length == 0) ? Time_SK1_Char_ConLai : int.Parse(Config.IniReadValue("GameServer", "Time_SK1_Char_ConLai").Trim());
			text = "ThienQuan_TyLePhanTramKinhNghiep_CoSo";
			ThienQuan_TyLePhanTramKinhNghiep_CoSo = (Config.IniReadValue("GameServer", "ThienQuan_TyLePhanTramKinhNghiep_CoSo").Trim().Length == 0) ? ThienQuan_TyLePhanTramKinhNghiep_CoSo : double.Parse(Config.IniReadValue("GameServer", "ThienQuan_TyLePhanTramKinhNghiep_CoSo").Trim());
			text = "ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo";
			ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo = (Config.IniReadValue("GameServer", "ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo").Trim().Length == 0) ? ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo : int.Parse(Config.IniReadValue("GameServer", "ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo").Trim());
			text = "ThienQuan_TyLePhanTramKinhNghiep_TangLen";
			ThienQuan_TyLePhanTramKinhNghiep_TangLen = (Config.IniReadValue("GameServer", "ThienQuan_TyLePhanTramKinhNghiep_TangLen").Trim().Length == 0) ? ThienQuan_TyLePhanTramKinhNghiep_TangLen : double.Parse(Config.IniReadValue("GameServer", "ThienQuan_TyLePhanTramKinhNghiep_TangLen").Trim());
			text = "TangTiLe_RotVatPham_TrongDiaDo";
			TangTiLe_RotVatPham_TrongDiaDo = (Config.IniReadValue("GameServer", "TangTiLe_RotVatPham_TrongDiaDo").Trim().Length == 0) ? TangTiLe_RotVatPham_TrongDiaDo : int.Parse(Config.IniReadValue("GameServer", "TangTiLe_RotVatPham_TrongDiaDo").Trim());
			text = "TieuThu_ThietBi";
			TieuThu_ThietBi = (Config.IniReadValue("GameServer", "TieuThu_ThietBi").Trim().Length == 0) ? TieuThu_ThietBi : int.Parse(Config.IniReadValue("GameServer", "TieuThu_ThietBi").Trim());
			text = "SoLuong_CapNhat_ThietBi";
			SoLuong_CapNhat_ThietBi = (Config.IniReadValue("GameServer", "SoLuong_CapNhat_ThietBi").Trim().Length == 0) ? SoLuong_CapNhat_ThietBi : int.Parse(Config.IniReadValue("GameServer", "SoLuong_CapNhat_ThietBi").Trim());
			text = "VatPhamThuong_MonChienID";
			VatPhamThuong_MonChienID = (Config.IniReadValue("GameServer", "VatPhamThuong_MonChienID").Trim().Length == 0) ? VatPhamThuong_MonChienID : int.Parse(Config.IniReadValue("GameServer", "VatPhamThuong_MonChienID").Trim());
			CoMoKhoa_PhatHienCongKich_HayKhong = (Config.IniReadValue("GameServer", "CoMoKhoa_PhatHienCongKich_HayKhong").Trim().Length == 0) ? CoMoKhoa_PhatHienCongKich_HayKhong : int.Parse(Config.IniReadValue("GameServer", "CoMoKhoa_PhatHienCongKich_HayKhong").Trim());
			KhoaNguoi_SoLanTanCong_CaoNhat = (Config.IniReadValue("GameServer", "KhoaNguoi_SoLanTanCong_CaoNhat").Trim().Length == 0) ? KhoaNguoi_SoLanTanCong_CaoNhat : int.Parse(Config.IniReadValue("GameServer", "KhoaNguoi_SoLanTanCong_CaoNhat").Trim());
			KhoaNguoi_CongKich_KiemTraThaoTac = (Config.IniReadValue("GameServer", "KhoaNguoi_CongKich_KiemTraThaoTac").Trim().Length == 0) ? KhoaNguoi_CongKich_KiemTraThaoTac : int.Parse(Config.IniReadValue("GameServer", "KhoaNguoi_CongKich_KiemTraThaoTac").Trim());
			BangHang1PhanThuong_DanhHieu = Config.IniReadValue("GameServer", "BangHang1PhanThuong_DanhHieu").Trim().Split(';');
			BangHang2PhanThuong_DanhHieu = Config.IniReadValue("GameServer", "BangHang2PhanThuong_DanhHieu").Trim().Split(';');
			BangHang3PhanThuong_DanhHieu = Config.IniReadValue("GameServer", "BangHang3PhanThuong_DanhHieu").Trim().Split(';');
			WhetherTheCurrentLineIsSilver = (Config.IniReadValue("GameServer", "WhetherTheCurrentLineIsSilver").Trim().Length == 0) ? WhetherTheCurrentLineIsSilver : int.Parse(Config.IniReadValue("GameServer", "WhetherTheCurrentLineIsSilver").Trim());
			CoMo_ThiTruongTraoDoiTienXu = (Config.IniReadValue("GameServer", "CoMo_ThiTruongTraoDoiTienXu").Trim().Length == 0) ? CoMo_ThiTruongTraoDoiTienXu : int.Parse(Config.IniReadValue("GameServer", "CoMo_ThiTruongTraoDoiTienXu").Trim());
			CongHienNguyenBao_Lenh = Config.IniReadValue("GameServer", "CongHienNguyenBao_Lenh").Trim();
			TrungSinh1 = Config.IniReadValue("GameServer", "TrungSinh1").Trim();
			Lenh_Buff_Cung = Config.IniReadValue("GameServer", "Lenh_Buff_Cung").Trim();
			Lenh_Buff_Bong = Config.IniReadValue("GameServer", "Lenh_Buff_Bong").Trim();
			Lenh_Buff_CPVP = Config.IniReadValue("GameServer", "Lenh_Buff_CPVP").Trim();
			Lenh_Buff_DaiPhu = Config.IniReadValue("GameServer", "Lenh_Buff_DaiPhu").Trim();
			Track = Config.IniReadValue("GameServer", "Track").Trim();
			Kick_UserName_TLC = Config.IniReadValue("GameServer", "Kick_UserName_TLC").Trim();
			Lenh_Party_1 = Config.IniReadValue("GameServer", "Lenh_Party_1").Trim();
			CTC = Config.IniReadValue("GameServer", "CTC").Trim();
			Add_Npc_map_TLC_se_thanh_Boss = Config.IniReadValue("GameServer", "Add_Npc_map_TLC_se_thanh_Boss").Trim();
			AddItemGM = Config.IniReadValue("GameServer", "AddItemGM").Trim();
			Add_Gold_GM = Config.IniReadValue("GameServer", "Add_Gold_GM").Trim();
			Add_Drop = Config.IniReadValue("GameServer", "Add_Drop").Trim();
			AddKyNang = Config.IniReadValue("GameServer", "AddKyNang").Trim();
			Add_Kinh_Nghiem = Config.IniReadValue("GameServer", "Add_Kinh_Nghiem").Trim();
			Lenh_Nhiem_Vu = Config.IniReadValue("GameServer", "Lenh_Nhiem_Vu").Trim();
			Add_Gold_Mem = Config.IniReadValue("GameServer", "Add_Gold_Mem").Trim();
			Xoa1 = Config.IniReadValue("GameServer", "Xoa1").Trim();
			Xoa2 = Config.IniReadValue("GameServer", "Xoa2").Trim();
			Xoa3 = Config.IniReadValue("GameServer", "Xoa3").Trim();
			Xoa4 = Config.IniReadValue("GameServer", "Xoa4").Trim();
			Xoa5 = Config.IniReadValue("GameServer", "Xoa5").Trim();
			XoaTatCa = Config.IniReadValue("GameServer", "XoaTatCa").Trim();
			Relog = Config.IniReadValue("GameServer", "Relog").Trim();
			DoiTheLuc = Config.IniReadValue("GameServer", "DoiTheLuc").Trim();
			Add_GM = Config.IniReadValue("GameServer", "Add_GM").Trim();
			DoiGioiTinh = Config.IniReadValue("GameServer", "DoiGioiTinh").Trim();
			XoaNguSac = Config.IniReadValue("GameServer", "XoaNguSac").Trim();
			ChuyenDoiKenh = Config.IniReadValue("GameServer", "ChuyenDoiKenh").Trim();
			XoaKimPhu = Config.IniReadValue("GameServer", "XoaKimPhu").Trim();
			XoaHuyenVuPhu = Config.IniReadValue("GameServer", "XoaHuyenVuPhu").Trim();
			XoaThuoc = Config.IniReadValue("GameServer", "XoaThuoc").Trim();
			TreoShopOffine = Config.IniReadValue("GameServer", "TreoShopOffine").Trim();
			TanHinh = Config.IniReadValue("GameServer", "TanHinh").Trim();
			XoaKhiCongChungThangThien = Config.IniReadValue("GameServer", "XoaKhiCongChungThangThien").Trim();
			XoaNguHanhVuKhi = Config.IniReadValue("GameServer", "XoaNguHanhVuKhi").Trim();
			XoaNguHanhTrangBi = Config.IniReadValue("GameServer", "XoaNguHanhTrangBi").Trim();
			Check_Info_Char = Config.IniReadValue("GameServer", "Check_Info_Char").Trim();
			Info_Thong_Tin = Config.IniReadValue("GameServer", "Info_Thong_Tin").Trim();
			NpcAdd = Config.IniReadValue("GameServer", "NpcAdd").Trim();
			CongHienNguyenBao_SoLuong = int.Parse(Config.IniReadValue("GameServer", "CongHienNguyenBao_SoLuong").Trim());
			CongHienNguyenBao_DiemVinhDu = int.Parse(Config.IniReadValue("GameServer", "CongHienNguyenBao_DiemVinhDu").Trim());
			PartitionNumber = Config.IniReadValue("GameServer", "PartitionNumber").Trim();
			Nguoi_GuiThu = (Config.IniReadValue("GameServer", "Nguoi_GuiThu").Trim() == "") ? Nguoi_GuiThu : Config.IniReadValue("GameServer", "Nguoi_GuiThu").Trim();
			DangNhap_TruyenThuNoiDung = (Config.IniReadValue("GameServer", "DangNhap_TruyenThuNoiDung").Trim() == "") ? DangNhap_TruyenThuNoiDung : Config.IniReadValue("GameServer", "DangNhap_TruyenThuNoiDung").Trim();
			AtPort = (Config.IniReadValue("GameServer", "AtPort").Trim() == "") ? AtPort : int.Parse(Config.IniReadValue("GameServer", "AtPort").Trim());
			TocDoLonNhat_VuotQuaSoLan_ThaoTac = (Config.IniReadValue("GameServer", "TocDoLonNhat_VuotQuaSoLan_ThaoTac").Trim().Length == 0) ? TocDoLonNhat_VuotQuaSoLan_ThaoTac : int.Parse(Config.IniReadValue("GameServer", "TocDoLonNhat_VuotQuaSoLan_ThaoTac").Trim());
			SoLan_VuotQuaChoPhep_Trong30Giay = (Config.IniReadValue("GameServer", "SoLan_VuotQuaChoPhep_Trong30Giay").Trim().Length == 0) ? SoLan_VuotQuaChoPhep_Trong30Giay : int.Parse(Config.IniReadValue("GameServer", "SoLan_VuotQuaChoPhep_Trong30Giay").Trim());
			Time_Delay_Item_8000008_Con_Lai = (Config.IniReadValue("GameServer", "Time_Delay_Item_8000008_Con_Lai").Trim().Length == 0) ? Time_Delay_Item_8000008_Con_Lai : int.Parse(Config.IniReadValue("GameServer", "Time_Delay_Item_8000008_Con_Lai").Trim());
			Time_Delay_Item_8000008_Cung = (Config.IniReadValue("GameServer", "Time_Delay_Item_8000008_Cung").Trim().Length == 0) ? Time_Delay_Item_8000008_Cung : int.Parse(Config.IniReadValue("GameServer", "Time_Delay_Item_8000008_Cung").Trim());
			Time_Delay_Item_8000008_ThichKhach = (Config.IniReadValue("GameServer", "Time_Delay_Item_8000008_ThichKhach").Trim().Length == 0) ? Time_Delay_Item_8000008_ThichKhach : int.Parse(Config.IniReadValue("GameServer", "Time_Delay_Item_8000008_ThichKhach").Trim());
			Time_Delay_Item_8000008_MLC = (Config.IniReadValue("GameServer", "Time_Delay_Item_8000008_MLC").Trim().Length == 0) ? Time_Delay_Item_8000008_MLC : int.Parse(Config.IniReadValue("GameServer", "Time_Delay_Item_8000008_MLC").Trim());
			KiemSoat_SoLuong_DropBoss = (Config.IniReadValue("GameServer", "KiemSoat_SoLuong_DropBoss").Trim().Length == 0) ? KiemSoat_SoLuong_DropBoss : Config.IniReadValue("GameServer", "KiemSoat_SoLuong_DropBoss").Trim();
			FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han = (Config.IniReadValue("GameServer", "FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han").Trim().Length == 0) ? FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han : int.Parse(Config.IniReadValue("GameServer", "FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han").Trim());
			SoLuong_Item_DropBoss = (Config.IniReadValue("GameServer", "SoLuong_Item_DropBoss").Trim().Length == 0) ? SoLuong_Item_DropBoss : int.Parse(Config.IniReadValue("GameServer", "SoLuong_Item_DropBoss").Trim());
			HuyenBotPhai = (Config.IniReadValue("GameServer", "HuyenBotPhai").Trim().Length == 0) ? HuyenBotPhai : int.Parse(Config.IniReadValue("GameServer", "HuyenBotPhai").Trim());
			TamTaQuan = (Config.IniReadValue("GameServer", "TamTaQuan").Trim().Length == 0) ? TamTaQuan : int.Parse(Config.IniReadValue("GameServer", "TamTaQuan").Trim());
			LieuChinhQuan = (Config.IniReadValue("GameServer", "LieuChinhQuan").Trim().Length == 0) ? LieuChinhQuan : int.Parse(Config.IniReadValue("GameServer", "LieuChinhQuan").Trim());
			ThanVoMon = (Config.IniReadValue("GameServer", "ThanVoMon").Trim().Length == 0) ? ThanVoMon : int.Parse(Config.IniReadValue("GameServer", "ThanVoMon").Trim());
			LieuThienPhu = (Config.IniReadValue("GameServer", "LieuThienPhu").Trim().Length == 0) ? LieuThienPhu : int.Parse(Config.IniReadValue("GameServer", "LieuThienPhu").Trim());
			NamMinhHieu = (Config.IniReadValue("GameServer", "NamMinhHieu").Trim().Length == 0) ? NamMinhHieu : int.Parse(Config.IniReadValue("GameServer", "NamMinhHieu").Trim());
			TungNguyetQuan = (Config.IniReadValue("GameServer", "TungNguyetQuan").Trim().Length == 0) ? TungNguyetQuan : int.Parse(Config.IniReadValue("GameServer", "TungNguyetQuan").Trim());
			BachVoQuan = (Config.IniReadValue("GameServer", "BachVoQuan").Trim().Length == 0) ? BachVoQuan : int.Parse(Config.IniReadValue("GameServer", "BachVoQuan").Trim());
			BacHaiBangCung = (Config.IniReadValue("GameServer", "BacHaiBangCung").Trim().Length == 0) ? BacHaiBangCung : int.Parse(Config.IniReadValue("GameServer", "BacHaiBangCung").Trim());
			NamLam = (Config.IniReadValue("GameServer", "NamLam").Trim().Length == 0) ? NamLam : int.Parse(Config.IniReadValue("GameServer", "NamLam").Trim());
			HoHapCoc = (Config.IniReadValue("GameServer", "HoHapCoc").Trim().Length == 0) ? HoHapCoc : int.Parse(Config.IniReadValue("GameServer", "HoHapCoc").Trim());
			XichThienGioi = (Config.IniReadValue("GameServer", "XichThienGioi").Trim().Length == 0) ? XichThienGioi : int.Parse(Config.IniReadValue("GameServer", "XichThienGioi").Trim());
			ThienDuSon = (Config.IniReadValue("GameServer", "ThienDuSon").Trim().Length == 0) ? ThienDuSon : int.Parse(Config.IniReadValue("GameServer", "ThienDuSon").Trim());
			ThanhDiaKiemHoang = (Config.IniReadValue("GameServer", "ThanhDiaKiemHoang").Trim().Length == 0) ? ThanhDiaKiemHoang : int.Parse(Config.IniReadValue("GameServer", "ThanhDiaKiemHoang").Trim());
			KhuLuyenTap1 = (Config.IniReadValue("GameServer", "KhuLuyenTap1").Trim().Length == 0) ? KhuLuyenTap1 : int.Parse(Config.IniReadValue("GameServer", "KhuLuyenTap1").Trim());
			KhuLuyenTap9 = (Config.IniReadValue("GameServer", "KhuLuyenTap9").Trim().Length == 0) ? KhuLuyenTap9 : int.Parse(Config.IniReadValue("GameServer", "KhuLuyenTap9").Trim());
			PhongThanKhau = (Config.IniReadValue("GameServer", "PhongThanKhau").Trim().Length == 0) ? PhongThanKhau : int.Parse(Config.IniReadValue("GameServer", "PhongThanKhau").Trim());
			Rate_Rot_Quest = (Config.IniReadValue("GameServer", "Rate_Rot_Quest").Trim().Length == 0) ? Rate_Rot_Quest : int.Parse(Config.IniReadValue("GameServer", "Rate_Rot_Quest").Trim());
			Item_Quest = (Config.IniReadValue("GameServer", "Item_Quest").Trim().Length == 0) ? Rate_Rot_Quest : int.Parse(Config.IniReadValue("GameServer", "Item_Quest").Trim());
			So_Luong_Quest = (Config.IniReadValue("GameServer", "So_Luong_Quest").Trim().Length == 0) ? So_Luong_Quest : int.Parse(Config.IniReadValue("GameServer", "So_Luong_Quest").Trim());
			ID_Quai = (Config.IniReadValue("GameServer", "ID_Quai").Trim().Length == 0) ? ID_Quai : int.Parse(Config.IniReadValue("GameServer", "ID_Quai").Trim());
			PhanThuongVoHuan_Quest = (Config.IniReadValue("GameServer", "PhanThuongVoHuan_Quest").Trim().Length == 0) ? PhanThuongVoHuan_Quest : int.Parse(Config.IniReadValue("GameServer", "PhanThuongVoHuan_Quest").Trim());
			LuongVoHuan_CuoiTuan = (Config.IniReadValue("GameServer", "LuongVoHuan_CuoiTuan").Trim().Length == 0) ? LuongVoHuan_CuoiTuan : int.Parse(Config.IniReadValue("GameServer", "LuongVoHuan_CuoiTuan").Trim());
			GioiHanVoHuanMoiNgay_Cap2 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap2").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap2 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap2").Trim());
			GioiHanVoHuanMoiNgay_Cap3 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap3").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap3 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap3").Trim());
			GioiHanVoHuanMoiNgay_Cap4 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap4").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap4 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap4").Trim());
			GioiHanVoHuanMoiNgay_Cap5 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap5").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap5 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap5").Trim());
			GioiHanVoHuanMoiNgay_Cap6 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap6").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap6 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap6").Trim());
			GioiHanVoHuanMoiNgay_Cap7 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap7").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap7 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap7").Trim());
			GioiHanVoHuanMoiNgay_Cap8 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap8").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap8 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap8").Trim());
			GioiHanVoHuanMoiNgay_Cap9 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap9").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap9 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap9").Trim());
			GioiHanVoHuanMoiNgay_Cap10 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap10").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap10 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap10").Trim());
			GioiHanVoHuanMoiNgay_Cap11 = (Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap11").Trim().Length == 0) ? GioiHanVoHuanMoiNgay_Cap11 : int.Parse(Config.IniReadValue("GameServer", "GioiHanVoHuanMoiNgay_Cap11").Trim());
			Time_TuHa_KTTX = (Config.IniReadValue("GameServer", "Time_TuHa_KTTX") == string.Empty) ? Time_TuHa_KTTX : double.Parse(Config.IniReadValue("GameServer", "Time_TuHa_KTTX"));
			TC_0_EXP = (Config.IniReadValue("GameServer", "TC_0_EXP") == string.Empty) ? TC_0_EXP : double.Parse(Config.IniReadValue("GameServer", "TC_0_EXP"));
			TC_1_EXP = (Config.IniReadValue("GameServer", "TC_1_EXP") == string.Empty) ? TC_1_EXP : double.Parse(Config.IniReadValue("GameServer", "TC_1_EXP"));
			TC_2_EXP = (Config.IniReadValue("GameServer", "TC_2_EXP") == string.Empty) ? TC_2_EXP : double.Parse(Config.IniReadValue("GameServer", "TC_2_EXP"));
			TC_3_EXP = (Config.IniReadValue("GameServer", "TC_3_EXP") == string.Empty) ? TC_3_EXP : double.Parse(Config.IniReadValue("GameServer", "TC_3_EXP"));
			TC_4_EXP = (Config.IniReadValue("GameServer", "TC_4_EXP") == string.Empty) ? TC_4_EXP : double.Parse(Config.IniReadValue("GameServer", "TC_4_EXP"));
			TC_5_EXP = (Config.IniReadValue("GameServer", "TC_5_EXP") == string.Empty) ? TC_5_EXP : double.Parse(Config.IniReadValue("GameServer", "TC_5_EXP"));
			TT_1_EXP = (Config.IniReadValue("GameServer", "TT_1_EXP") == string.Empty) ? TT_1_EXP : double.Parse(Config.IniReadValue("GameServer", "TT_1_EXP"));
			TT_2_EXP = (Config.IniReadValue("GameServer", "TT_2_EXP") == string.Empty) ? TT_2_EXP : double.Parse(Config.IniReadValue("GameServer", "TT_2_EXP"));
			TT_3_EXP = (Config.IniReadValue("GameServer", "TT_3_EXP") == string.Empty) ? TT_3_EXP : double.Parse(Config.IniReadValue("GameServer", "TT_3_EXP"));
			TT_4_EXP = (Config.IniReadValue("GameServer", "TT_4_EXP") == string.Empty) ? TT_4_EXP : double.Parse(Config.IniReadValue("GameServer", "TT_4_EXP"));
			TT_5_EXP = (Config.IniReadValue("GameServer", "TT_5_EXP") == string.Empty) ? TT_5_EXP : double.Parse(Config.IniReadValue("GameServer", "TT_5_EXP"));
			TT_6_EXP = (Config.IniReadValue("GameServer", "TT_6_EXP") == string.Empty) ? TT_6_EXP : double.Parse(Config.IniReadValue("GameServer", "TT_6_EXP"));
			Dame_Skill_Dao = (Config.IniReadValue("GameServer", "Dame_Skill_Dao") == string.Empty) ? Dame_Skill_Dao : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_Dao"));
			Dame_Skill_Kiem = (Config.IniReadValue("GameServer", "Dame_Skill_Kiem") == string.Empty) ? Dame_Skill_Kiem : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_Kiem"));
			Dame_Skill_Thuong = (Config.IniReadValue("GameServer", "Dame_Skill_Thuong") == string.Empty) ? Dame_Skill_Thuong : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_Thuong"));
			Dame_Skill_Kich_Hoat_Cung = (Config.IniReadValue("GameServer", "Dame_Skill_Kich_Hoat_Cung") == string.Empty) ? Dame_Skill_Kich_Hoat_Cung : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_Kich_Hoat_Cung"));
			Dame_Skill_DaiPhu = (Config.IniReadValue("GameServer", "Dame_Skill_DaiPhu") == string.Empty) ? Dame_Skill_DaiPhu : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_DaiPhu"));
			Dame_Skill_Ninja = (Config.IniReadValue("GameServer", "Dame_Skill_Ninja") == string.Empty) ? Dame_Skill_Ninja : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_Ninja"));
			Dame_Skill_CamSu = (Config.IniReadValue("GameServer", "Dame_Skill_CamSu") == string.Empty) ? Dame_Skill_CamSu : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_CamSu"));
			Dame_Skill_HBQ = (Config.IniReadValue("GameServer", "Dame_Skill_HBQ") == string.Empty) ? Dame_Skill_HBQ : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_HBQ"));
			Dame_Skill_DHL = (Config.IniReadValue("GameServer", "Dame_Skill_DHL") == string.Empty) ? Dame_Skill_DHL : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_DHL"));
			Dame_Skill_QuyenSu = (Config.IniReadValue("GameServer", "Dame_Skill_QuyenSu") == string.Empty) ? Dame_Skill_QuyenSu : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_QuyenSu"));
			Dame_Skill_MLC = (Config.IniReadValue("GameServer", "Dame_Skill_MLC") == string.Empty) ? Dame_Skill_MLC : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_MLC"));
			Dame_Skill_TH = (Config.IniReadValue("GameServer", "Dame_Skill_TH") == string.Empty) ? Dame_Skill_TH : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_TH"));
			Dame_Skill_TN = (Config.IniReadValue("GameServer", "Dame_Skill_TN") == string.Empty) ? Dame_Skill_TN : double.Parse(Config.IniReadValue("GameServer", "Dame_Skill_TN"));
			Skill_KichHoat_Cung_11x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_11x") == string.Empty) ? Skill_KichHoat_Cung_11x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_11x"));
			Skill_KichHoat_Cung_12x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_12x") == string.Empty) ? Skill_KichHoat_Cung_12x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_12x"));
			Skill_KichHoat_Cung_13x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_13x") == string.Empty) ? Skill_KichHoat_Cung_13x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_13x"));
			Skill_KichHoat_Cung_14x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_14x") == string.Empty) ? Skill_KichHoat_Cung_14x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_14x"));
			Skill_KichHoat_Cung_15x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_15x") == string.Empty) ? Skill_KichHoat_Cung_15x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_15x"));
			Skill_KichHoat_Cung_16x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_16x") == string.Empty) ? Skill_KichHoat_Cung_16x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Cung_16x"));
			Skill_KichHoat_Nin_11x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_11x") == string.Empty) ? Skill_KichHoat_Nin_11x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_11x"));
			Skill_KichHoat_Nin_12x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_12x") == string.Empty) ? Skill_KichHoat_Nin_12x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_12x"));
			Skill_KichHoat_Nin_13x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_13x") == string.Empty) ? Skill_KichHoat_Nin_13x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_13x"));
			Skill_KichHoat_Nin_14x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_14x") == string.Empty) ? Skill_KichHoat_Nin_14x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_14x"));
			Skill_KichHoat_Nin_15x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_15x") == string.Empty) ? Skill_KichHoat_Nin_15x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_15x"));
			Skill_KichHoat_Nin_16x = (Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_16x") == string.Empty) ? Skill_KichHoat_Nin_16x : double.Parse(Config.IniReadValue("GameServer", "Skill_KichHoat_Nin_16x"));
			QuyenSu_PK_Combo_1 = (Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_1") == string.Empty) ? QuyenSu_PK_Combo_1 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_1"));
			QuyenSu_PK_Combo_2 = (Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_2") == string.Empty) ? QuyenSu_PK_Combo_2 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_2"));
			QuyenSu_PK_Combo_3 = (Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_3") == string.Empty) ? QuyenSu_PK_Combo_3 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_3"));
			QuyenSu_PK_Combo_4 = (Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_4") == string.Empty) ? QuyenSu_PK_Combo_1 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_4"));
			QuyenSu_PK_Combo_5 = (Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_5") == string.Empty) ? QuyenSu_PK_Combo_1 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_PK_Combo_5"));
			QuyenSu_Monster_Combo_1 = (Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_1") == string.Empty) ? QuyenSu_Monster_Combo_1 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_1"));
			QuyenSu_Monster_Combo_2 = (Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_2") == string.Empty) ? QuyenSu_Monster_Combo_2 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_2"));
			QuyenSu_Monster_Combo_3 = (Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_3") == string.Empty) ? QuyenSu_Monster_Combo_3 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_3"));
			QuyenSu_Monster_Combo_4 = (Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_4") == string.Empty) ? QuyenSu_Monster_Combo_1 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_4"));
			QuyenSu_Monster_Combo_5 = (Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_1") == string.Empty) ? QuyenSu_Monster_Combo_5 : double.Parse(Config.IniReadValue("GameServer", "QuyenSu_Monster_Combo_5"));
			LoggerVersion = (Config.IniReadValue("GameServer", "LoggerVersion") == string.Empty) ? LoggerVersion : double.Parse(Config.IniReadValue("GameServer", "LoggerVersion"));
			PhaiChang_HoTroMoRong_CacVatPham_ChuSo = (Config.IniReadValue("GameServer", "PhaiChang_HoTroMoRong_CacVatPham_ChuSo").Trim().Length == 0) ? PhaiChang_HoTroMoRong_CacVatPham_ChuSo : int.Parse(Config.IniReadValue("GameServer", "PhaiChang_HoTroMoRong_CacVatPham_ChuSo").Trim());
			VuKhiPK_RoiDoBen = (Config.IniReadValue("GameServer", "VuKhiPK_RoiDoBen").Trim().Length == 0) ? VuKhiPK_RoiDoBen : int.Parse(Config.IniReadValue("GameServer", "VuKhiPK_RoiDoBen").Trim());
			DoPhongNguPK_RoiDoBen = (Config.IniReadValue("GameServer", "DoPhongNguPK_RoiDoBen").Trim().Length == 0) ? DoPhongNguPK_RoiDoBen : int.Parse(Config.IniReadValue("GameServer", "DoPhongNguPK_RoiDoBen").Trim());
			ThoiGianTreoMay_AnToan = (Config.IniReadValue("GameServer", "ThoiGianTreoMay_AnToan").Trim().Length == 0) ? ThoiGianTreoMay_AnToan : int.Parse(Config.IniReadValue("GameServer", "ThoiGianTreoMay_AnToan").Trim());
			TyLe_TangCuong_VatPham = (Config.IniReadValue("GameServer", "TyLe_TangCuong_VatPham").Trim().Length == 0) ? TyLe_TangCuong_VatPham : double.Parse(Config.IniReadValue("GameServer", "TyLe_TangCuong_VatPham").Trim());
			Newversion = (Config.IniReadValue("GameServer", "NEWVERSION").Trim() == "") ? Newversion : int.Parse(Config.IniReadValue("GameServer", "NEWVERSION").Trim());
			Eventx2 = (Config.IniReadValue("GameServer", "Eventx2").Trim().Length == 0) ? Eventx2 : int.Parse(Config.IniReadValue("GameServer", "Eventx2").Trim());
			Eventx3 = (Config.IniReadValue("GameServer", "Eventx3").Trim().Length == 0) ? Eventx3 : int.Parse(Config.IniReadValue("GameServer", "Eventx3").Trim());
			text = "Event_Bonus_Rate";
			Event_Bonus_Rate = (Config.IniReadValue("GameServer", "Event_Bonus_Rate").Trim().Length == 0) ? Event_Bonus_Rate : double.Parse(Config.IniReadValue("GameServer", "Event_Bonus_Rate").Trim());
			Open_Auto_GiftCode = (Config.IniReadValue("GameServer", "Open_Auto_GiftCode").Trim() == "") ? Open_Auto_GiftCode : int.Parse(Config.IniReadValue("GameServer", "Open_Auto_GiftCode").Trim());
			Config_Auto_GiftCode = Config.IniReadValue("GameServer", "Config_Auto_GiftCode").Trim().Split(';');
			ChoPhepKetHonCungGioi = Config.IniReadValue("GameServer", "ChoPhepKetHonCungGioi").Trim().Length == 0;
			QueueAttack = Config.IniReadValue("GameServer", "QueueAttack").Trim().Length == 0;
			
			// Attack Limiter Configuration
			TMQH_LongCD_Rate_0 = Config.IniReadValue("hanbaoquan", "TMQH_LongCD_Rate_0").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_LongCD_Rate_0").Trim()) : 2;
			TMQH_LongCD_Rate_1 = Config.IniReadValue("hanbaoquan", "TMQH_LongCD_Rate_1").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_LongCD_Rate_1").Trim()) : 1.5;
			TMQH_Rate_0 = Config.IniReadValue("hanbaoquan", "TMQH_Rate_0").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_Rate_0").Trim()) : 1;
			TMQH_Rate_1 = Config.IniReadValue("hanbaoquan", "TMQH_Rate_1").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_Rate_1").Trim()) : 0;
			TMQH_LongCD_DMG_1 = Config.IniReadValue("hanbaoquan", "TMQH_LongCD_DMG_1").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_LongCD_DMG_1").Trim()) : 0.4;
			TMQH_LongCD_DMG_1_PK = Config.IniReadValue("hanbaoquan", "TMQH_LongCD_DMG_1_PK").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_LongCD_DMG_1_PK").Trim()) : 0.65;
			TMQH_LongCD_DMG_2 = Config.IniReadValue("hanbaoquan", "TMQH_LongCD_DMG_2").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_LongCD_DMG_2").Trim()) : 0.8;
			TMQH_LongCD_DMG_2_PK = Config.IniReadValue("hanbaoquan", "TMQH_LongCD_DMG_2_PK").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_LongCD_DMG_2_PK").Trim()) : 1.3;
			TMQH_DMG_1 = Config.IniReadValue("hanbaoquan", "TMQH_DMG_1").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_DMG_1").Trim()) : 0.2;
			TMQH_DMG_2 = Config.IniReadValue("hanbaoquan", "TMQH_DMG_2").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_DMG_2").Trim()) : 0.4;
			TMQH_DMG_1_PK = Config.IniReadValue("hanbaoquan", "TMQH_DMG_1_PK").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_DMG_1_PK").Trim()) : 0.2;
			TMQH_DMG_2_PK = Config.IniReadValue("hanbaoquan", "TMQH_DMG_2_PK").Trim().Length != 0 ? double.Parse(Config.IniReadValue("hanbaoquan", "TMQH_DMG_2_PK").Trim()) : 0.4;
			NPC_HP_AMP = Config.IniReadValue("monster", "NPC_HP_AMP").Trim().Length == 0 ? 100 : int.Parse(Config.IniReadValue("monster", "NPC_HP_AMP").Trim());
			NPC_DEF_AMP = Config.IniReadValue("monster", "NPC_DEF_AMP").Trim().Length == 0 ? 100 : int.Parse(Config.IniReadValue("monster", "NPC_DEF_AMP").Trim());
			NPC_ATK_AMP = Config.IniReadValue("monster", "NPC_ATK_AMP").Trim().Length == 0 ? 100 : int.Parse(Config.IniReadValue("monster", "NPC_ATK_AMP").Trim());
			NPC_ACC_AMP = Config.IniReadValue("monster", "NPC_ACC_AMP").Trim().Length == 0 ? 100 : int.Parse(Config.IniReadValue("monster", "NPC_ACC_AMP").Trim());
			NPC_EVA_AMP = Config.IniReadValue("monster", "NPC_EVA_AMP").Trim().Length == 0 ? 100 : int.Parse(Config.IniReadValue("monster", "NPC_EVA_AMP").Trim());
			MONSTER_DEF_AMP_MAGIC = Config.IniReadValue("monster", "MONSTER_DEF_AMP_MAGIC").Trim().Length == 0 ? 100 : int.Parse(Config.IniReadValue("monster", "MONSTER_DEF_AMP_MAGIC").Trim());
			MONSTER_DEF_AMP_PHYSICAL = Config.IniReadValue("monster", "MONSTER_DEF_AMP_PHYSICAL").Trim().Length == 0 ? 100 : int.Parse(Config.IniReadValue("monster", "MONSTER_DEF_AMP_PHYSICAL").Trim());

			// Attack Limiter System Configuration
			World.AttackLimiter_Enabled = Config.IniReadValue("GameServer", "AttackLimiter_Enabled").Trim().ToLower() == "true";
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Configuration file " + ex.Message +ex.StackTrace);
			throw;
		}
	}
    public static void delNpc(int mapId, int npcId)
    {
        try
        {
            if (MapList.TryGetValue(mapId, out var map))
            {
                foreach (var npc in map.npcTemplate.Values.ToList())
                {
                    if (npc.FLD_PID == npcId)
                    {
                        map.RemoveNpcFromMapClass(npc.NPC_SessionID);
                        LogHelper.WriteLine(LogLevel.Info, $"Đã xóa NPC {npc.Name} (ID: {npc.FLD_PID}) khỏi bản đồ {mapId}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi xóa NPC: {ex.Message}");
        }
    }
    internal static void SendMailCodNotificationByAdmin(int WorldId, int sessionID)
    {
        if (WorldId == ServerID)
        {
            var player = FindPlayerBySession(sessionID);
            if (player != null)
            {
                SendingClass sendingClass = new();
                sendingClass.Write4(1);
                sendingClass.Write2(0);
                player.Client?.SendPak(sendingClass, 12065, sessionID, true);
            }
        }
        else
        {
            conn.Transmit($"MAILCOD_ADMIN_NOTI|{WorldId}|{sessionID}");
        }

    }
    public static NpcClass AddNpcNeo(int npcId, float x, float y, int mapId, string thanmadaichien = "",
         float face1 = 0f, float face2 = 0f, int TMDC_Team = 0, bool worldBoss = false, int marked = 0, double disposeTime = 0)
    {
        try
        {
            if (MonsterTemplateList.TryGetValue(npcId, out var value))
            {
                NpcClass npcClass = new()
                {
                    FLD_PID = value.fld_pid,
                    Name = value.fld_name,
                    Level = value.fld_level,
                    Rxjh_Exp = value.fld_exp,
                    Rxjh_X = x,
                    Rxjh_Y = y,
                    Rxjh_Z = 15f,
                    Rxjh_cs_X = x,
                    Rxjh_cs_Y = y,
                    Rxjh_cs_Z = 15f,
                    Rxjh_Map = mapId,
                    IsNpc = 0,
                    FLD_FACE1 = face1,
                    FLD_FACE2 = face2,
                    //Max_Rxjh_HP = value.Rxjh_HP,
                    //Rxjh_HP = value.Rxjh_HP,
                    FLD_AT = value.fld_at,
                    FLD_DF = 0,
                    FLD_AUTO = value.fld_auto,
                    FLD_BOSS = value.fld_boss,
                    FLD_FreeDrop = value.FLD_FreeDrop,
                    Rxjh_Evasion = value.FLD_Evasion,
                    Rxjh_Accuracy = value.FLD_Accuracy,
                    FLD_NEWTIME = 10,
                    QuaiXuatHien_DuyNhatMotLan = true,
                    timeNpcDie = DateTime.Now,
                    timeNpcRevival = DateTime.MinValue,
                    IsWorldBoss = worldBoss, // Boss thế giới
                    MarkedType = marked, // Tạo cờ khi chết
                    DisPoseTime = disposeTime // THời gian tự động chết
                };
                npcClass.SetMaxHP(value.fld_hp);
                npcClass.SetHp(value.fld_hp);

                if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
                {
                    value2.AddNpcToMapClass(npcClass);
                }
                else
                {
                    MapClass mapClass = new()
                    {
                        MapID = npcClass.Rxjh_Map
                    };
                    mapClass.AddNpcToMapClass(npcClass);
                    MapList.Add(mapClass.MapID, mapClass);
                }
				
				npcClass.AddToAOI();
                npcClass.NPC_Attack();
                if (disposeTime > 0)
                {
                    npcClass.InitiateDisposeTime();
                }

                return npcClass;
            }
            else
            {
                LogHelper.WriteLine(LogLevel.Error, "Không tìm thấy Npc cần add " + npcId);
                return null;
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "Tăng cường đổ lỗi  [" + npcId + "]error：" + ex);
            return null;
        }
    }


}