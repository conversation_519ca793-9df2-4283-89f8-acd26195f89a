using System;

namespace RxjhServer.NpcManager
{
    /// <summary>
    /// Simplified distance calculation helper - replaces complex DistanceHelper
    /// Removes caching and complex optimizations for better maintainability
    /// </summary>
    public static class DistanceHelper
    {
        /// <summary>
        /// Calculate actual distance between two points
        /// </summary>
        /// <param name="x1">First point X coordinate</param>
        /// <param name="y1">First point Y coordinate</param>
        /// <param name="x2">Second point X coordinate</param>
        /// <param name="y2">Second point Y coordinate</param>
        /// <returns>Actual distance</returns>
        public static double CalculateDistance(float x1, float y1, float x2, float y2)
        {
            var deltaX = x2 - x1;
            var deltaY = y2 - y1;
            return Math.Sqrt(deltaX * deltaX + deltaY * deltaY);
        }
        /// <summary>
        /// Check if two points are within range using squared distance (no Math.Sqrt)
        /// </summary>
        /// <param name="x1">First point X coordinate</param>
        /// <param name="y1">First point Y coordinate</param>
        /// <param name="x2">Second point X coordinate</param>
        /// <param name="y2">Second point Y coordinate</param>
        /// <param name="range">Maximum distance range</param>
        /// <returns>True if points are within range</returns>
        public static bool IsWithinRange(float x1, float y1, float x2, float y2, int range)
        {
            var deltaX = x2 - x1;
            var deltaY = y2 - y1;
            var distanceSquared = deltaX * deltaX + deltaY * deltaY;
            var rangeSquared = range * range;
            return distanceSquared <= rangeSquared;
        }

        /// <summary>
        /// Check if two points are within range using squared distance
        /// Alias for IsWithinRange for compatibility
        /// </summary>
        public static bool IsWithinRangeSquared(float x1, float y1, float x2, float y2, int range)
        {
            return IsWithinRange(x1, y1, x2, y2, range);
        }
    }
}
