using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.AOI;

namespace RxjhServer.NpcManager
{
    /// <summary>
    /// Simplified NPC management system - replaces NPCBehaviorManager, NPCTimerManager
    /// Reduces complexity while maintaining core functionality
    /// </summary>
    public static class SimpleNPCManager
    {
        #region Configuration
        // Simplified distance constants
        public static int ATTACK_RANGE = 15;
        public static int CHASE_RANGE = 100;
        public static int RETURN_RANGE = 200;
        public static int ATTACK_SPEED_MS = 2500;
        #endregion

        #region Timer Management
        private static readonly Timer _globalTimer = new(100); // 1 second interval
        private static readonly ConcurrentDictionary<int, NPCInfo> _npcs = new();
        private static bool _initialized = false;

        static SimpleNPCManager()
        {
            Initialize();
        }

        private static void Initialize()
        {
            if (_initialized) return;
            
            _globalTimer.Elapsed += ProcessAllNPCs;
            _globalTimer.AutoReset = true;
            _globalTimer.Enabled = true;
            _initialized = true;
            
            LogHelper.WriteLine(LogLevel.Info, "SimpleNPCManager initialized");
        }
        #endregion

        #region NPC Registration
        /// <summary>
        /// Register NPC for management
        /// </summary>
        public static void RegisterNPC(NpcClass npc)
        {
            if (npc == null) return;
            
            var info = new NPCInfo
            {
                NPC = npc,
                LastUpdate = DateTime.Now,
                CurrentBehavior = SimpleBehavior.Idle,
                LastAttackTime = DateTime.MinValue
            };
            
            _npcs.AddOrUpdate(npc.NPC_SessionID, info, (key, existing) => info);
        }

        /// <summary>
        /// Unregister NPC
        /// </summary>
        public static void UnregisterNPC(int sessionId)
        {
            _npcs.TryRemove(sessionId, out _);
        }
        #endregion

        #region Core Processing
        /// <summary>
        /// Main timer event - processes all NPCs
        /// </summary>
        private static void ProcessAllNPCs(object sender, ElapsedEventArgs e)
        {
            try
            {
                var now = DateTime.Now;
                var toRemove = new List<int>();

                foreach (var kvp in _npcs)
                {
                    var npcInfo = kvp.Value;
                    var npc = npcInfo.NPC;

                    try
                    {
                        // Remove invalid NPCs
                        if (npc.NPC_Removed)
                        {
                            toRemove.Add(kvp.Key);
                            continue;
                        }

                        // Process respawn for dead NPCs
                        if (npc.NPCDeath)
                        {
                            ProcessRespawn(npc, npcInfo, now);
                            continue;
                        }

                        // Process behavior for alive NPCs
                        ProcessNPCBehavior(npc, npcInfo, now);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"Error processing NPC {npc.NPC_SessionID}: {ex.Message}");
                    }
                }

                // Clean up removed NPCs
                foreach (var id in toRemove)
                {
                    _npcs.TryRemove(id, out _);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in ProcessAllNPCs: {ex.Message}");
            }
        }

        /// <summary>
        /// Process individual NPC behavior
        /// </summary>
        private static void ProcessNPCBehavior(NpcClass npc, NPCInfo npcInfo, DateTime now)
        {
            // Skip non-combat NPCs
            if (npc.IsNpc != 0 || npc.FLD_AT <= 0) return;

            // Find target player
            var targetPlayer = FindTargetPlayer(npc);
            
            // Determine behavior (simplified to 2 states)
            var newBehavior = DetermineBehavior(npc, targetPlayer, npcInfo);
            
            // Execute behavior if changed
            if (newBehavior != npcInfo.CurrentBehavior)
            {
                npcInfo.CurrentBehavior = newBehavior;
                npcInfo.BehaviorStartTime = now;
            }

            ExecuteBehavior(npc, npcInfo, targetPlayer, now);
            npcInfo.LastUpdate = now;
        }

        /// <summary>
        /// Find target player for NPC
        /// </summary>
        private static Players FindTargetPlayer(NpcClass npc)
        {
            try
            {
                var nearbyPlayers = npc.GetNearbyPlayers(CHASE_RANGE);
                Players closestPlayer = null;
                double closestDistance = double.MaxValue;

                foreach (var player in nearbyPlayers)
                {
                    if (player?.Client?.Running != true || player.GMMode == 8 || 
                        player.MapID != npc.Rxjh_Map || player.NhanVat_HP <= 0) 
                        continue;

                    var distance = DistanceHelper.CalculateDistance(
                        npc.Rxjh_X, npc.Rxjh_Y, player.PosX, player.PosY);

                    if (distance <= CHASE_RANGE && distance < closestDistance)
                    {
                        closestDistance = distance;
                        closestPlayer = player;
                    }
                }

                return closestPlayer;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error finding target for NPC {npc.NPC_SessionID}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Determine NPC behavior (simplified to 2 states)
        /// </summary>
        private static SimpleBehavior DetermineBehavior(NpcClass npc, Players targetPlayer, NPCInfo npcInfo)
        {
            // Check if too far from spawn - return to spawn
            var distanceFromSpawn = DistanceHelper.CalculateDistance(
                npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_cs_X, npc.Rxjh_cs_Y);

            if (distanceFromSpawn > RETURN_RANGE)
            {
                return SimpleBehavior.Returning;
            }

            // If has target and NPC is aggressive or has been attacked
            if (targetPlayer != null && (npc.FLD_AUTO == 1 || npc.GetNearbyPlayersCount() > 0))
            {
                return SimpleBehavior.Attacking;
            }

            // Default to idle
            return SimpleBehavior.Idle;
        }

        /// <summary>
        /// Execute NPC behavior
        /// </summary>
        private static void ExecuteBehavior(NpcClass npc, NPCInfo npcInfo, Players targetPlayer, DateTime now)
        {
            switch (npcInfo.CurrentBehavior)
            {
                case SimpleBehavior.Attacking:
                    if (targetPlayer != null)
                        ExecuteAttacking(npc, npcInfo, targetPlayer, now);
                    break;

                case SimpleBehavior.Returning:
                    ExecuteReturning(npc, npcInfo);
                    break;

                case SimpleBehavior.Idle:
                default:
                    ExecuteIdle(npc, npcInfo);
                    break;
            }
        }

        /// <summary>
        /// Execute attacking behavior
        /// </summary>
        private static void ExecuteAttacking(NpcClass npc, NPCInfo npcInfo, Players targetPlayer, DateTime now)
        {
            var distance = DistanceHelper.CalculateDistance(
                npc.Rxjh_X, npc.Rxjh_Y, targetPlayer.PosX, targetPlayer.PosY);

            if (distance <= ATTACK_RANGE)
            {
                // Close enough to attack
                npc.AutomaticMoveEnabled = false;
                npc.AutomaticAttackEnabled = true;

                // Check attack speed
                if ((now - npcInfo.LastAttackTime).TotalMilliseconds >= ATTACK_SPEED_MS)
                {
                    npc.ProcessAutomaticAttack();
                    npcInfo.LastAttackTime = now;
                }
            }
            else
            {
                // Chase player
                npc.AutomaticMoveEnabled = true;
                npc.AutomaticAttackEnabled = true;
                npc.SendMovingData(targetPlayer.PosX, targetPlayer.PosY, 55, 2);
                npc.AddPlayerTarget(targetPlayer);
            }
        }

        /// <summary>
        /// Execute returning behavior
        /// </summary>
        private static void ExecuteReturning(NpcClass npc, NPCInfo npcInfo)
        {
            npc.AutomaticMoveEnabled = true;
            npc.AutomaticAttackEnabled = false;
            npc.PlayCw = null;
            npc.SendMovingData(npc.Rxjh_cs_X, npc.Rxjh_cs_Y, 30, 2);
        }

        /// <summary>
        /// Execute idle behavior
        /// </summary>
        private static void ExecuteIdle(NpcClass npc, NPCInfo npcInfo)
        {
            npc.AutomaticMoveEnabled = false;
            npc.AutomaticAttackEnabled = false;
            npc.PlayCw = null;
        }

        /// <summary>
        /// Process NPC respawn
        /// </summary>
        private static void ProcessRespawn(NpcClass npc, NPCInfo npcInfo, DateTime now)
        {
            try
            {
                npc.ProcessAutomaticRespawn();
                npcInfo.CurrentBehavior = SimpleBehavior.Idle;
                npcInfo.LastAttackTime = DateTime.MinValue;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in respawn for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }
        #endregion

        #region Counter Attack
        /// <summary>
        /// Handle immediate counter-attack when NPC is attacked
        /// </summary>
        public static void HandleCounterAttack(NpcClass npc, Players attacker)
        {
            if (npc?.NPCDeath != false || attacker?.NhanVat_HP <= 0) return;

            try
            {
                npc.AddPlayerTarget(attacker);
                
                if (_npcs.TryGetValue(npc.NPC_SessionID, out var npcInfo))
                {
                    npcInfo.CurrentBehavior = SimpleBehavior.Attacking;
                    npcInfo.BehaviorStartTime = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in counter attack: {ex.Message}");
            }
        }
        #endregion


    }

    /// <summary>
    /// Simplified NPC behavior states (reduced from 4 to 2 main states)
    /// </summary>
    public enum SimpleBehavior
    {
        Idle,       // Standing still or random movement
        Attacking,  // Chasing and attacking player
        Returning   // Returning to spawn (sub-state of Idle)
    }

    /// <summary>
    /// Simplified NPC information
    /// </summary>
    public class NPCInfo
    {
        public NpcClass NPC { get; set; }
        public DateTime LastUpdate { get; set; }
        public SimpleBehavior CurrentBehavior { get; set; }
        public DateTime BehaviorStartTime { get; set; }
        public DateTime LastAttackTime { get; set; }
    }
}
